---
description: 
globs: 
alwaysApply: false
---
# Evolve - 开发规范文档 v2.1

## 引言

本规范是Evolve iOS应用AI辅助开发的最高行为准则。AI在生成任何代码、UI元素、文档或提出任何建议时，必须严格遵守本规范。本规范整合了产品设计、UI设计、技术架构的要求，旨在确保AI产出高度一致、符合规范、易于维护，并能顺利实现项目目标。特别强调，AI在完成指定的开发任务后，有责任按照本规范第十三章的要求，及时更新项目根目录下的README.md文件，记录开发进度和重要变更。

## 一、核心原则与全局约束

### 1. 严格遵循已定方案

- **唯一依据**：以提供的最新版本项目文档（产品需求文档、UI设计规范文档、技术架构文档）为唯一依据。
- **文档优先级**：文档之间如有冲突，以本开发规范为最高优先级。其次是技术架构文档，然后是产品需求文档和UI设计规范。
- **禁止幻想与臆测**：所有产出必须有据可依，不得自行创造未明确的功能、UI样式、数据结构或业务逻辑。此原则同样适用于README.md的更新。

### 2. 代码质量与可维护性原则

- **简洁性**：代码应简洁、高效、易读，避免不必要的复杂性。
- **一致性**：代码风格、命名规范、文件结构保持一致。
- **可测试性**：设计便于单元测试和UI测试的代码结构。
- **可维护性**：清晰的代码组织和注释，便于后期维护和扩展。
- **🚨 调试代码清理**：**严禁在生产代码中保留print()调试语句**，所有调试代码必须在提交前清理。

### 3. iOS设计与开发规范

- **HIG遵循**：严格遵守Apple的《Human Interface Guidelines》(HIG)。
- **原生体验**：优先使用iOS原生控件和交互模式，确保用户体验的一致性。
- **无障碍支持**：提供基本的无障碍支持，如VoiceOver和动态字体。

### 4. 模块化与可复用性

- **组件化设计**：UI组件和功能模块应高度模块化和可复用。
- **依赖注入**：使用依赖注入降低模块间耦合度。
- **抽象接口**：使用协议定义模块间接口，便于测试和替换实现。

## 二、编码规范与质量标准

### 1. Swift编码规范

- **代码风格**：四空格缩进，每行不超过120个字符
- **命名约定**：小驼峰式变量/函数，大驼峰式类型
- **安全编程**：使用可选类型，避免强制解包
- **🚨 调试代码清理**：严禁保留print()调试语句

### 2. SwiftUI开发规范

- **状态管理最佳实践**：
  - @State：单一视图本地状态
  - @Binding：双向数据绑定
  - @ObservedObject/@StateObject：ViewModel引用
  - @Environment：全局依赖注入
- **视图组织**：复杂视图分解为子组件，使用ViewModifier封装样式

### 2.1 SwiftUI Sheet状态管理规范（iOS 17.0+兼容）

#### 🚨 核心原则：单一状态源管理

**强制要求（解决多重Sheet冲突）**：
- **统一状态管理**：所有Sheet状态必须在单一ViewModel中集中管理，避免分散在多个@State变量中
- **枚举驱动模式**：使用枚举定义所有可能的Sheet类型，确保同时只有一个Sheet处于活跃状态
- **@MainActor线程安全**：所有Sheet状态管理必须在主线程执行，与项目MVVM架构保持一致

#### 🔒 标准实现模式（遵循EA命名规范）

**1. Sheet状态枚举定义**：
```swift
// ✅ 正确：统一Sheet状态枚举（遵循EA命名前缀）
enum EASheetType: Identifiable, CaseIterable {
    case habitCreation
    case habitDetail(EAHabit)
    case userProfile
    case settings
    case aiChat
    case paymentSubscription
    
    var id: String {
        switch self {
        case .habitCreation:
            return "habitCreation"
        case .habitDetail(let habit):
            return "habitDetail_\(habit.id)"
        case .userProfile:
            return "userProfile"
        case .settings:
            return "settings"
        case .aiChat:
            return "aiChat"
        case .paymentSubscription:
            return "paymentSubscription"
        }
    }
}
```

**2. ViewModel中的Sheet状态管理**：
```swift
// ✅ 正确：遵循项目MVVM架构和@MainActor规范
@MainActor
class EASheetManager: ObservableObject {
    // 单一状态源 - 解决多重Sheet冲突的核心
    @Published var activeSheet: EASheetType?
    
    // Sheet显示控制
    func presentSheet(_ sheetType: EASheetType) {
        // 确保同时只有一个Sheet
        activeSheet = sheetType
    }
    
    func dismissSheet() {
        activeSheet = nil
    }
    
    // 便捷方法 - 遵循项目命名规范
    func presentHabitCreation() {
        presentSheet(.habitCreation)
    }
    
    func presentHabitDetail(_ habit: EAHabit) {
        presentSheet(.habitDetail(habit))
    }
    
    func presentUserProfile() {
        presentSheet(.userProfile)
    }
    
    func presentSettings() {
        presentSheet(.settings)
    }
    
    func presentAIChat() {
        presentSheet(.aiChat)
    }
    
    func presentPaymentSubscription() {
        presentSheet(.paymentSubscription)
    }
}
```

**3. View中的Sheet集成**：
```swift
// ✅ 正确：在主视图中统一管理所有Sheet
struct EAMainView: View {
    @StateObject private var sheetManager = EASheetManager()
    
    var body: some View {
        NavigationView {
            // 主要内容
            ContentView()
        }
        // 统一Sheet管理 - 解决冲突的关键
        .sheet(item: $sheetManager.activeSheet) { sheetType in
            sheetContent(for: sheetType)
        }
        .environmentObject(sheetManager) // 传递给子视图
    }
    
    // Sheet内容构建器
    @ViewBuilder
    private func sheetContent(for sheetType: EASheetType) -> some View {
        switch sheetType {
        case .habitCreation:
            EAHabitCreationView()
                .environmentObject(sheetManager)
        case .habitDetail(let habit):
            EAHabitDetailView(habit: habit)
                .environmentObject(sheetManager)
        case .userProfile:
            EAUserProfileView()
                .environmentObject(sheetManager)
        case .settings:
            EASettingsView()
                .environmentObject(sheetManager)
        case .aiChat:
            EAAIChatView()
                .environmentObject(sheetManager)
        case .paymentSubscription:
            EAPaymentSubscriptionView()
                .environmentObject(sheetManager)
        }
    }
}
```

**4. 子视图中的Sheet调用**：
```swift
// ✅ 正确：子视图通过环境对象调用Sheet
struct EAHabitListView: View {
    @EnvironmentObject private var sheetManager: EASheetManager
    @Query private var habits: [EAHabit]
    
    var body: some View {
        List(habits) { habit in
            EAHabitCard(habit: habit)
                .onTapGesture {
                    // 通过统一管理器显示Sheet
                    sheetManager.presentHabitDetail(habit)
                }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("添加习惯") {
                    sheetManager.presentHabitCreation()
                }
            }
        }
    }
}
```

#### 🚫 严格禁止的做法

**1. 多个@State变量管理Sheet**：
```swift
// ❌ 错误：多个Sheet状态变量（导致冲突）
struct BadExampleView: View {
    @State private var showHabitCreation = false
    @State private var showUserProfile = false
    @State private var showSettings = false
    // 这种方式会导致多重Sheet冲突
}
```

**2. 分散的Sheet管理**：
```swift
// ❌ 错误：在不同视图中分别管理Sheet
struct AnotherBadView: View {
    @State private var showSheet = false
    // 与其他视图的Sheet状态不协调
}
```

**3. 直接在子视图中定义Sheet**：
```swift
// ❌ 错误：子视图直接管理Sheet状态
struct BadChildView: View {
    @State private var showDetail = false
    // 应该通过父视图的统一管理器
}
```

#### 🔧 iOS 17.0+兼容性保障

**1. 使用现代SwiftUI语法**：
```swift
// ✅ iOS 17.0+兼容的Sheet语法
.sheet(item: $sheetManager.activeSheet) { sheetType in
    // 内容构建
}

// ✅ 支持iOS 17.0+的onChange语法
.onChange(of: sheetManager.activeSheet) { oldValue, newValue in
    // Sheet状态变化处理
}
```

**2. 环境值传递**：
```swift
// ✅ 正确的环境值传递（iOS 17.0+兼容）
.environmentObject(sheetManager)
```

**3. 状态恢复支持**：
```swift
// ✅ 支持状态恢复的Sheet管理
@MainActor
class EASheetManager: ObservableObject {
    @Published var activeSheet: EASheetType?
    
    // 状态恢复
    func restoreSheetState(from userDefaults: UserDefaults) {
        // 实现状态恢复逻辑
    }
    
    func saveSheetState(to userDefaults: UserDefaults) {
        // 实现状态保存逻辑
    }
}
```

#### 🛠️ 最佳实践指南

**1. Sheet内容预加载**：
```swift
// ✅ 推荐：Sheet内容懒加载
@ViewBuilder
private func sheetContent(for sheetType: EASheetType) -> some View {
    Group {
        switch sheetType {
        case .habitCreation:
            EAHabitCreationView()
        // 其他case...
        }
    }
    .onAppear {
        // Sheet出现时的初始化逻辑
    }
}
```

**2. Sheet关闭处理**：
```swift
// ✅ 正确的Sheet关闭处理
struct EAHabitCreationView: View {
    @EnvironmentObject private var sheetManager: EASheetManager
    
    var body: some View {
        NavigationView {
            // 内容
            VStack {
                // 表单内容
            }
            .navigationTitle("创建习惯")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        sheetManager.dismissSheet()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        // 保存逻辑
                        sheetManager.dismissSheet()
                    }
                }
            }
        }
    }
}
```

**3. 错误处理与降级**：
```swift
// ✅ Sheet错误处理
@MainActor
class EASheetManager: ObservableObject {
    @Published var activeSheet: EASheetType?
    @Published var sheetError: Error?
    
    func presentSheet(_ sheetType: EASheetType) {
        do {
            // 验证Sheet可以显示
            try validateSheetPresentation(sheetType)
            activeSheet = sheetType
            sheetError = nil
        } catch {
            sheetError = error
            // 记录错误但不阻塞UI
            print("Sheet presentation error: \(error)")
        }
    }
    
    private func validateSheetPresentation(_ sheetType: EASheetType) throws {
        // 验证逻辑
    }
}
```

#### ⚡ 性能优化要求

**1. Sheet内容延迟加载**：
- Sheet内容应在需要时才创建，避免预先创建所有可能的Sheet视图
- 使用@ViewBuilder确保视图的懒加载特性

**2. 状态更新优化**：
- Sheet状态变更必须在主线程执行
- 避免频繁的Sheet状态切换，实现防抖机制

**3. 内存管理**：
- Sheet关闭时及时释放相关资源
- 避免在Sheet中持有强引用循环

#### 🔍 调试与问题排查

**1. Sheet状态日志**：
```swift
// ✅ 调试时的状态日志（生产环境需清理）
func presentSheet(_ sheetType: EASheetType) {
    #if DEBUG
    print("🔍 Presenting sheet: \(sheetType.id)")
    #endif
    activeSheet = sheetType
}
```

**2. 状态一致性检查**：
```swift
// ✅ 开发阶段的状态验证
private func validateSheetState() {
    #if DEBUG
    assert(Thread.isMainThread, "Sheet state must be updated on main thread")
    #endif
}
```

**⚠️ 重要提醒**：
- 所有Sheet状态管理必须遵循项目的EA命名前缀规范
- 必须与项目的MVVM架构和@MainActor要求保持一致
- 严禁在生产代码中保留调试print()语句
- 确保与SwiftData模型的集成遵循项目的数据管理规范

### 3. MVVM实现规范

- **ViewModel要求**：
  - 必须标记@MainActor确保UI更新在主线程
  - 使用@Published暴露状态
  - 不直接引用View，保持单向依赖
- **错误处理**：统一的错误类型和用户友好的错误消息

### 4. SwiftData开发规范

- **🚨 关键约束（iOS 17.0-18.5+兼容性）**：
  - **完整双向关系强制要求（绝对不可妥协）**：所有@Relationship必须有对应的反向关系(inverse)，避免iOS 18.2+Schema验证失败
  - **关系对称性验证**：每个关系在两个模型中都要定义，确保inverse路径正确
  - **UUID自动生成**：不在init中手动设置UUID，让系统自动生成
  - **关系集合初始化**：所有集合类型的关系属性必须在init中初始化，而非设置默认值
  - **关系赋值顺序（iOS 18+强制要求）**：所有关系属性（包括集合和单对象）必须在对象插入ModelContext后再赋值，禁止在init或插入前赋值关系，确保兼容iOS 18+。典型顺序：1. 创建对象（不赋值关系）→ 2. 插入ModelContext → 3. 赋值关系属性 → 4. 保存Context。
  - **每次模型变更必须更新迁移计划**：维护SchemaMigrationPlan的完整性

- **🔒 Apple官方ModelContainer配置规范（WWDC 2023/2024确认）**：
  ```swift
  // ✅ 正确：多模型使用可变参数语法（Apple官方标准）
  let container = try ModelContainer(for: 
      EAUser.self,
      EAHabit.self,
      EACompletion.self,
      EAUserSettings.self
  )
  
  // ✅ 正确：单模型直接传递
  let container = try ModelContainer(for: EAUser.self)
  
  // ❌ 错误：使用数组语法（编译错误）
  let container = try ModelContainer(for: [EAUser.self, EAHabit.self]) // 编译错误
  ```

- **🔒 Apple官方Preview配置规范（WWDC 2024确认）**：
  ```swift
  // ✅ 正确：SwiftUI Preview必须配置ModelContainer
  struct SampleData: PreviewModifier {
      static func makeSharedContext() throws -> ModelContainer {
          let config = ModelConfiguration(isStoredInMemoryOnly: true)
          let container = try ModelContainer(for: 
              EAUser.self,
              EAHabit.self,
              EACompletion.self,
              configurations: config
          )
          return container
      }
      
      func body(content: Content, context: ModelContainer) -> some View {
          content.modelContainer(context)
      }
  }
  
  // ✅ 使用方式
  #Preview(traits: .sampleData) {
      ContentView()
  }
  ```

- **🔒 完整双向inverse关系原则（绝对不可妥协）**：
  ```swift
  // ✅ 正确：一端使用@Relationship(inverse:)，另一端使用普通属性
  @Model
  class EAHabit {
      var id: UUID = UUID()
      var name: String
      var creationDate: Date = Date()
      var iconName: String
      var targetFrequency: Int
      var isActive: Bool = true
      
      // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
      @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit) 
      var completions: [EACompletion]
      
      // ✅ 正确：反向关系使用普通属性
      var user: EAUser?
      
      // ✅ 正确：SwiftData原生支持基础类型数组（iOS 18.2+日志警告是系统级问题）
      var selectedWeekdays: [Int] = []
      var reminderTimes: [String] = []
      
      init(name: String, iconName: String, targetFrequency: Int) {
          self.name = name
          self.iconName = iconName
          self.targetFrequency = targetFrequency
          self.completions = [] // 在init中初始化，而非属性默认值
      }
  }
  
  @Model
  class EACompletion {
      var id: UUID = UUID()
      var date: Date = Date()
      var completionNote: String?
      var energyLevel: Int = 5 // 1-10
      
      // ✅ 正确：使用普通属性，SwiftData自动维护inverse关系
      var habit: EAHabit?
      
      init(completionNote: String? = nil, energyLevel: Int = 5) {
          self.completionNote = completionNote
          self.energyLevel = energyLevel
      }
  }
  
  @Model
  class EAUser {
      var id: UUID = UUID()
      var username: String
      var email: String?
      var creationDate: Date = Date()
      
      // ✅ 正确：定义inverse指向另一端的属性（不设置默认值）
      @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
      var habits: [EAHabit]
      
      init(username: String, email: String? = nil) {
          self.username = username
          self.email = email
          self.habits = [] // 在init中初始化，而非属性默认值
      }
  }
  ```

- **🚨 关系设计核心规则**：
  - **单端inverse规则**：双向关系只在一端使用@Relationship(inverse:)，另一端用普通属性声明
  - **类型匹配要求**：inverse的KeyPath必须精确指向对方模型的对应属性
  - **关系基数正确**：一对一、一对多、多对多关系的类型必须完全匹配
  - **属性名一致性**：关系两端的属性名必须精确对应，包括大小写和单复数
  - **🔒 核心原则（绝对不可妥协）**：双向关系只在一端使用@Relationship(inverse:)，另一端用普通属性声明，这是正确且必要的做法，能有效避免循环引用
  - **强制要求**：必须在一端使用@Relationship(inverse:)，另一端可以是普通属性（但需类型匹配）
  - **绝对禁止**：两端同时使用@Relationship或完全省略inverse，会导致关系未正确链接

- **🚫 严格禁止的做法**：
  - **删除inverse参数**：绝对不能通过删除inverse来"解决"编译错误
  - **两端同时使用@Relationship**：会导致关系冲突和循环引用
  - **完全省略inverse**：所有双向关系必须在一端正确定义inverse
  - **单向关系设计**：需要双向访问的关系必须在两端正确定义
  - **关系默认值设置**：@Model会忽略关系属性的默认值（如var crews: [CrewModel] = []），实际值由SwiftData管理。关系属性不应设置默认值，应在init中初始化
  - **构造器中创建关联对象**：避免在init中创建关联对象，会导致运行时错误（Failed to find container）

- **🛠️ 关系定义最佳实践**：
  ```swift
  // ✅ 正确示例（参考Apple官方Flight和CrewModel模式）
  @Model class Flight {
      // 正确定义逆向指向CrewModel的flight属性
      @Relationship(inverse: \CrewModel.flight) 
      var crews: [CrewModel] // 一对多关系，不设置默认值
      
      init() {
          self.crews = [] // 在init中初始化
      }
  }
  @Model class CrewModel {
      var flight: Flight? // 普通属性，无需@Relationship
  }
  
  // ✅ 项目中的一对一关系示例
  @Model class EAUser {
      @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
      var settings: EAUserSettings?
  }
  @Model class EAUserSettings {
      var user: EAUser? // 普通属性，无需@Relationship
  }
  
  // ✅ 项目中的一对多关系示例
  @Model class EAUser {
      @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
      var habits: [EAHabit]
      
      init(username: String) {
          self.habits = [] // 在init中初始化
      }
  }
  @Model class EAHabit {
      var user: EAUser? // 普通属性，无需@Relationship
  }
  
  // ✅ 避免多对多关系，使用中间模型
  // 不推荐：直接多对多关系
  // 推荐：通过中间模型实现（如FlightCrewAssignment）
  @Model class EAHabitTagAssignment {
      var habitId: UUID
      var tagId: UUID
      var assignedDate: Date = Date()
  }
  ```

- **性能优化规范**：
  - **简单@Query使用**：仅用于简单谓词，避免复杂逻辑导致iOS 18+性能问题
  - **复杂查询@ModelActor处理**：使用@ModelActor处理复杂数据操作，避免@Query性能问题
  - **批量操作优化**：使用ModelContext.transaction提高性能
  - **内存管理**：及时释放大型查询结果，避免内存泄漏

- **🔍 常见错误预防**：
  ```swift
  // ❌ 错误1：两端都使用@Relationship（冗余，会导致冲突）
  @Model class EAHabit {
      @Relationship(inverse: \EAUser.habits)
      var user: EAUser?
  }
  @Model class EAUser {
      @Relationship(inverse: \EAHabit.user) // 冗余，会导致冲突
      var habits: [EAHabit]
  }
  
  // ❌ 错误2：完全省略inverse（会导致关系未正确链接）
  @Model class Flight {
      var crews: [CrewModel] // 缺失inverse定义
  }
  @Model class CrewModel {
      var flight: Flight?
  }
  
  // ❌ 错误3：KeyPath指向错误的属性
  @Model class EAHabit {
      @Relationship(inverse: \EAUser.habit) // 错误：EAUser中是habits不是habit
      var user: EAUser?
  }
  
  // ❌ 错误4：类型不匹配
  @Model class EAHabit {
      @Relationship(inverse: \EAUser.habits)
      var users: [EAUser] // 错误：应该是单个user，不是数组
  }
  
  // ❌ 错误5：在构造器中创建关联对象
  init() {
      self.tag = Tag(name: "default") // 错误：关联对象未插入ModelContext
  }
  // ✅ 正确：应改为显式插入上下文
  let tag = Tag(name: "default")
  modelContext.insert(tag)
  self.tag = tag
  ```

- **版本兼容性保障**：
  - **关系完整性检查**：定期验证所有关系的inverse定义正确性
  - **Schema迁移计划**：每次模型变更都要更新SchemaMigrationPlan
  - **开发阶段数据库重置**：模型变更后强制删除数据库文件，重新创建干净环境
  - **生产环境迁移策略**：使用版本化迁移，支持CloudKit同步
  - **线程安全要求**：所有SwiftData操作必须在主线程执行（用@MainActor或DispatchQueue.main），避免并发冲突
  - **删除规则合理配置**：使用@Relationship的删除规则（.cascade：删除父对象时自动删除子对象；.noAction：手动管理依赖）
  - **自动推断限制认知**：SwiftData仅在特定场景下自动推断逆向关系（如两端均为Optional的一对一关系），其他情况必须显式声明inverse。需显式声明inverse的场景包括：关系一端为Non-Optional、多对多关系（两端均为Non-Optional）
  - **调试工具应用**：使用Xcode的Memory Graph Debugger检测循环引用，开启com.apple.CoreData.ConcurrencyDebug 1捕获线程问题

- **🛠️ 最佳实践示例**：
  ```swift
  // ✅ 推荐：简单查询使用@Query
  @Query(filter: #Predicate<EAHabit> { $0.isActive == true }) 
  var activeHabits: [EAHabit]
  
  // ✅ 复杂查询使用@ModelActor
  @ModelActor
  actor EAHabitDataManager {
      func performComplexQuery() throws -> [EAHabit] {
          let descriptor = FetchDescriptor<EAHabit>(
              predicate: #Predicate { $0.isActive == true },
              sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
          )
          return try modelContext.fetch(descriptor)
      }
      
      // 通过关系查询完成记录
      func fetchCompletionsForHabit(_ habitId: UUID) throws -> [EACompletion] {
          let descriptor = FetchDescriptor<EACompletion>(
              predicate: #Predicate { completion in
                  completion.habit?.id == habitId
              },
              sortBy: [SortDescriptor(\.date, order: .reverse)]
          )
          return try modelContext.fetch(descriptor)
      }
  }
  ```

- **🚫 严格禁止的做法**：
  - **删除inverse参数**：绝对不能通过删除inverse来"解决"编译错误
  - **单向关系设计**：所有双向关系必须在两端正确定义
  - **关系默认值设置**：@Model会忽略关系属性的默认值
  - **构造器中创建关联对象**：避免在init中创建关联对象，会导致运行时错误
  - **两端同时使用@Relationship**：会导致关系冲突和循环引用

## 三、项目结构与命名规范

### 1. 文件结构规范

严格遵循《技术架构文档》中定义的目录结构，关键目录如下：

- **Evolve/Core/**: 核心服务和共享功能
- **Evolve/Common/**: 通用工具和扩展
- **Evolve/Features/**: 按业务划分的功能模块
- **Evolve/UIComponents/**: 可复用UI组件

任何在此结构下新增的主要组件或模块，AI均需在README.md中记录。

### 2. 命名规范

- **文件命名**：
  - 一个文件一个主要类型，文件名反映主要内容。
  - 使用后缀表示文件类型，如:
    - `xxxView.swift`: SwiftUI视图
    - `xxxViewModel.swift`: 视图模型
    - `xxxService.swift`: 服务类
    - `xxx+Extension.swift`: 扩展
- **类型命名**：
  - **模型**：名词，如`Habit`
  - **视图**：以"View"结尾，如`HabitDetailView`
  - **视图模型**：以"ViewModel"结尾，如`HabitDetailViewModel`
  - **服务**：以"Service"或"Manager"结尾，如`NotificationService`
  - **组件**：反映功能，如`EnergyFlowProgressBar`
- **方法命名**：
  - 使用动词开头，如`fetchHabits()`、`completeHabit(id:)`
  - 异步方法明确标识，如`async loadData()`
  - 事件处理方法应表明时机，如`onAppear()`、`didTapButton()`
- **属性命名**：
  - 使用名词，如`habits`、`userName`
  - 布尔值应表明状态，如`isLoading`、`hasData`
  - 私有属性使用_前缀，如`private var _cache`

### 2.1 命名前缀与防冲突策略（优化版）

为符合iOS开发的简洁性与可读性，同时避免命名冲突，特制定以下优化策略（**此为项目中应遵循的最终命名规范**）：

- **简洁前缀原则**：
  - 遵循Apple框架命名风格（如NS、UI、CA等），项目自定义类型统一使用简洁的`EA`前缀（代表 Evolve App）。
  - 保持命名简洁易读，避免过长的多级前缀。

- **简化命名指南**：
  - **数据模型**：`EAHabit`、`EACompletion`、`EAUser` (注意：原`HabitCompletion`调整为`EACompletion`以更明确，原`UserProfile`调整为`EAUser`)
  - **视图组件**：`EAButton`、`EATextField`、`EAHabitCard` (注意：原`HabitCardView`简化为`EAHabitCard`，与UI规范文档统一)
  - **服务类**：`EANetworkService`、`EAAIService` (AI相关服务可保留`EAAI`以示区分)
  - **工具类**：`EADateFormatter`、`EALogger` (原`Logger`调整为`EALogger`)
  - **视图模型**：`EAHabitViewModel`、`EAUserViewModel`

- **命名冲突处理**：
  - **有限使用扩展前缀**：仅对少数确实容易与系统类型、常用第三方库高频词汇产生严重命名冲突的类型，才考虑在`EA`后加入细分领域标识。
    - 示例：
        - `Message` (若用于聊天等场景且易与系统`MessageUI`等冲突) → `EAChatMessage` 或 `EAAIMessage`
        - `Task` (若用于自定义任务且易与Swift Concurrency的`Task`冲突) → `EAHabitTask` 或 `EABackgroundTask`
        - `Event` (若用于自定义事件且易与多种事件类型冲突) → `EAAnalyticsEvent` 或 `EAUserInteractionEvent`
  - **其他类型优先保持简洁**：不存在显著冲突风险的类型直接使用`EA`前缀 + 描述性名称 + 类型后缀。

- **后缀使用**：
  - 使用功能性后缀清晰区分类型用途：`View`、`ViewModel`、`Service`、`Manager`、`Model` (通常模型名称本身已明确，可省略`Model`后缀，如`EAHabit`)、`Card`、`Cell`等。
  - 常见后缀示例：
    - 视图：`EAHabitListView`、`EAUserView`
    - 视图模型：`EAHabitViewModel`、`EAUserViewModel`
    - 服务：`EANetworkService`、`EAStorageService`

- **文件命名**：
  - 文件名必须与文件内定义的主要Swift类型（class, struct, enum, protocol）保持一致，例如 `EAButton.swift` 文件包含 `EAButton` 类型。
  - SwiftUI视图文件通常以其主要View的名称命名，如 `EAHabitCard.swift`。
  - 扩展文件使用清晰的命名，如 `Date+EAFormatting.swift` (表示对`Date`类型的扩展，提供了`EA`项目相关的格式化功能)。

- **向后兼容性与类型别名**：
  - 如果项目从旧的命名规范迁移，或为了逐步采纳新规范，可以使用`typealias`机制保持向后兼容：
  ```swift
  // 新命名 - 简洁明了
  struct EAAIMessage { /* ... */ } // 假设这是处理AI消息的模型
  // 向后兼容旧代码中可能使用的无前缀或不同前缀的名称
  typealias Message = EAAIMessage
  typealias CoreAIResponse = EAAIMessage // 示例：如果旧代码用了另一个名字
  ```

- **SwiftData模型特殊处理**：
  - SwiftData模型也应保持命名简洁，例如 `EAHabit`。如果模型名称本身已足够清晰表意，则无需额外添加`Model`后缀。
  - 模型变更时，需同步更新相关的类型别名（如果用到）和数据迁移计划（SchemaMigrationPlan）。
  - 模型间的关系（@Relationship）应使用清晰的命名反映其关联，例如 `habit.completionRecords`（而不是泛化的 `habit.completions`，如果`EACompletion`是新命名）。

此命名策略遵循iOS开发的简洁性传统，同时解决项目中的命名冲突问题。它旨在贴近Apple原生框架的命名风格，易于开发者理解和使用。**之前文档中若有其他版本的"命名前缀与防冲突策略"，均以此"优化版"为准。**

### 3. 组件复用规范

- **抽取阈值**：
  - 当UI元素在3个或以上地方重复使用时，应封装为组件。
  - 当业务逻辑超过50行代码时，考虑拆分为多个辅助方法。
- **参数传递**：
  - 使用明确的参数传递组件配置，避免全局状态。
  - 回调函数使用闭包，如`onCompletion: @escaping () -> Void`。
- **组件接口**：
  - 定义清晰的组件API表面，隐藏内部实现细节。
  - 使用@ViewBuilder提供内容定制能力。
  - 支持SwiftUI环境值传播。
- **新组件记录**：AI创建的任何可复用组件（UI组件、服务等），都必须在README.md中记录其名称、完整路径（相对于项目根目录，例如 `Evolve/UIComponents/EAButton.swift`）及简要功能。

## 四、UI与视觉效果规范

### 1. 设计系统实现

严格遵循《UI设计规范文档》，实现以下关键要素：

- **色彩系统**：所有颜色定义在`Evolve/Assets.xcassets`的Color Set中，使用语义化命名。
- **排版系统**：使用系统字体，定义清晰的文字层级。
- **间距系统**：使用基础间距单位(8pt)的倍数定义间距。
- **组件库**：实现规范中定义的所有UI组件，保持一致的视觉风格。

### 2. 视觉表现原则

- **生态隐喻**：UI元素应体现"生态"和"能量流转"的核心隐喻。
- **动效原则**：动效应自然、流畅、有意义，不过度使用。
- **状态反馈**：各交互元素应有清晰的状态反馈（普通、按下、禁用等）。
- **一致性**：保持全应用的视觉一致性，包括颜色、字体、间距和动效。

### 3. 自适应布局

- **安全区域**：严格遵守安全区域，特别是刘海屏和Home Indicator区域。
- **设备适配**：支持所有当前iPhone尺寸，包括最小的iPhone SE。
- **方向适配**：支持竖屏模式；如需支持横屏，需特别优化布局。
- **动态字体**：响应系统字体大小设置变化。

### 4. 无障碍支持

- **VoiceOver**：为所有交互元素提供合适的标签和提示。
- **动态字体**：支持系统动态字体大小调整。
- **对比度**：保持足够的文本对比度，遵循WCAG 2.1 AA标准。
- **减少动画**：响应系统"减少动画"设置。

## 五、开发流程与质量保证

### 1. 组件开发流程

- **创建新组件**：
  - 先检查UIComponents目录，避免重复开发
  - 遵循EA命名前缀规范
  - 必须包含SwiftUI预览
  - 更新README.md记录新组件

### 2. 错误处理标准

- **错误分类**：网络错误、数据错误、业务错误
- **用户体验**：友好的错误提示和恢复建议
- **降级策略**：缓存数据、离线模式

### 3. 内存管理要求

- **循环引用防范**：正确使用[weak self]
- **资源释放**：及时取消Task和移除监听器
- **大对象管理**：图片延迟加载和适当缓存

## 六、API集成与安全规范

### 1. AI服务集成

- **接口封装**：EAAIService处理API通信，EAAIManager管理会话
- **类型安全**：使用Codable协议，定义请求/响应模型
- **错误处理**：网络、解析、AI模型错误的统一处理
- **安全存储**：API密钥使用Keychain，不硬编码

### 2. StoreKit支付集成

- **产品ID规范**：`evolve_pro_[duration]`格式
- **交易验证**：使用StoreKit 2的VerificationResult
- **安全存储**：交易信息使用Keychain存储
- **记录要求**：成功交易记录到EAPaymentRecord模型

### 3. 网络与安全

- **HTTPS强制**：所有网络请求使用HTTPS
- **网络监控**：使用NWPathMonitor监控网络状态
- **离线策略**：缓存关键数据，支持离线查看
- **重试机制**：网络错误自动重试，最多3次

## 七、测试与质量保证

### 1. SwiftUI预览规范

- **必须包含**：所有SwiftUI视图必须包含预览
- **预览数据**：使用PreviewData.swift中的模拟数据
- **多场景支持**：不同状态和设备尺寸的预览

### 2. 测试要求

- **单元测试**：所有ViewModel和Service需有单元测试
- **测试覆盖率**：核心业务逻辑≥80%
- **依赖模拟**：使用协议和依赖注入便于测试
- **UI测试**：测试核心用户流程，使用可访问性标识符

### 3. 通知系统规范

- **权限管理**：合适时机请求通知权限
- **智能调度**：基于用户设定时间和行为模式
- **个性化内容**：根据教练风格生成不同提醒内容
- **通知管理**：用户可查看和管理已安排的通知

## 八、安全与性能规范

### 1. 数据安全要求

- **敏感数据**：使用Keychain存储API密钥、支付信息
- **本地存储**：非敏感数据使用UserDefaults
- **网络安全**：强制HTTPS，实现证书验证
  - 支付相关信息必须使用Keychain存储。
- **数据传输**：
  - 网络传输使用HTTPS，实现证书固定。
  - 传输敏感数据时使用额外加密。
  - 支付数据传输遵循PCI DSS标准。
- **数据备份**：SwiftData内容包含在iOS备份中，无需额外设置。
- **数据导出安全**：
  - 导出文件使用临时目录，完成后及时清理。
  - 敏感信息在导出时适当脱敏。

### 2. 用户隐私

- **数据收集**：
  - 清晰说明收集的数据类型和用途。
  - 提供控制选项，允许用户选择退出非必要数据收集。
  - 行为分析数据匿名化处理。
- **隐私策略**：
  - 提供简明的隐私政策。
  - 符合应用商店和相关法律要求。
- **数据控制**：
  - 允许用户导出或删除其数据。
  - 提供清晰的数据使用控制界面。
  - 使用EADataExportService实现GDPR合规的数据导出。

### 3. 身份验证

- **认证方式**：
  - 支持密码、生物认证(Face ID/Touch ID)和第三方身份验证。
  - 实现安全的密码重置流程。
- **会话管理**：
  - 合理的会话过期时间。
  - 检测异常登录行为。
- **敏感操作保护**：
  - 重要操作(如支付、删除账户)需额外验证。
  - 提供操作审计日志。

### 4. 支付安全增强

- **交易验证**：
  - 使用StoreKit 2的内置验证机制。
  - 实现防欺诈检测逻辑。
- **收据管理**：
  - 安全存储和传输交易收据。
  - 定期验证订阅状态。
- **退款处理**：
  - 正确处理退款和订阅取消。
  - 及时更新用户权限状态。

## 十、AI开发约束与输出质量控制

### 1. AI角色定位与交互规范

- **核心角色**：AI必须作为"私人教练、引导者、对话伙伴、赋能者"，而非简单的工具或助手。
- **对话优先原则**：
  - 所有核心功能优先采用对话式交互设计
  - AI回复必须具备情感温度和个性化特征
  - 支持多轮对话和上下文记忆
  - 避免机械化的模板回复
- **情境感知要求**：
  - AI必须能够识别用户当前的情绪状态
  - 根据对话上下文调整回复风格和内容
  - 考虑用户的个性偏好和历史行为
  - 在适当时机主动提供支持和建议

### 2. 代码复杂度控制

- **圈复杂度**：单个函数/方法的圈复杂度控制在10以下。
- **方法长度**：单个函数/方法代码行数控制在50行以内。
- **参数数量**：方法参数不超过5个；超过时考虑使用配置对象。
- **嵌套深度**：控制逻辑嵌套不超过3层。

### 3. 智能算法性能规范

- **AI响应时间**：AI对话响应时间控制在3秒以内。
- **情境分析效率**：用户情绪和行为分析计算时间控制在1秒以内。
- **个性化推荐**：内容推荐计算应在后台线程进行，不阻塞UI。
- **对话上下文管理**：
  - 对话历史保持在合理范围内（最近20条）
  - 长期记忆数据定期压缩和归档
  - 个性化模型增量更新，避免重复计算
- **缓存策略**：
  - AI响应结果适当缓存，避免重复计算
  - 用户个性档案缓存24小时
  - 行为分析结果缓存12小时
  - 情境感知数据实时更新

### 4. AI交互质量标准

- **对话自然度**：
  - AI回复必须符合中文表达习惯
  - 避免生硬的翻译腔和机器味
  - 根据用户偏好调整语言风格（正式/轻松/幽默）
- **情感表达能力**：
  - 能够识别和回应用户的情绪状态
  - 在适当时候表达共情和理解
  - 提供有温度的鼓励和支持
- **个性化程度**：
  - 基于用户历史数据提供个性化建议
  - 记住用户的偏好和习惯模式
  - 动态调整教练风格和沟通方式
- **主动性要求**：
  - 能够主动识别用户可能遇到的问题
  - 在关键时机提供干预和支持
  - 预测用户需求并提前准备解决方案

### 5. AI成本控制开发规范

- **分层调用策略**：
  - **高优先级场景**：用户主动求助、危机干预、重大里程碑（必须调用AI API）
  - **中优先级场景**：每日洞察生成、习惯创建建议（缓存24小时后调用）
  - **低优先级场景**：日常提醒、基础统计（优先本地处理）
- **缓存机制要求**：
  - 用户画像缓存7天有效期
  - AI洞察缓存24小时有效期
  - 行为分析结果缓存3天有效期
  - 相似问题回复缓存1天有效期
- **降级策略实现**：
  - AI服务不可用时必须有高质量备选方案
  - 本地智能引擎能独立处理基础场景
  - 模板化消息系统确保用户体验连续性
- **成本监控要求**：
  - 精确跟踪每个用户的AI调用成本
  - 实现调用频率限制（用户级别、功能级别）
  - 建立调用配额管理（日/周/月限制）
  - 提供成本优化建议和预警

### 6. 智能分析引擎开发规范

- **行为模式检测**：
  - 识别用户习惯完成的时间规律（本地算法处理）
  - 检测连续失败模式和触发因素（AI深度分析）
  - 分析用户交互偏好和成功因素（混合处理）
- **情绪状态分析**：
  - 基于文本输入分析情绪状态（AI处理）
  - 通过行为数据推断情绪变化（本地+AI结合）
  - 识别需要情绪支持的关键时机（AI处理）
- **预测性洞察**：
  - 预测用户今日习惯完成概率（本地算法）
  - 生成个性化的每日洞察（AI生成+模板结合）
  - 预警潜在的习惯中断风险（AI分析）
- **性能要求**：
  - 行为模式检测准确性≥80%
  - 情绪状态分析响应时间≤1秒
  - 预测性洞察计算时间≤3秒

### 7. 维护特性要求

- **注释规范**：
  - 为复杂逻辑添加清晰注释。
  - 使用`// MARK: - Section Name`进行代码分节。
  - 使用`/// Documentation comments`为公共API生成文档。
- **硬编码控制**：
  - 禁止使用魔法数字和硬编码字符串。
  - 常量定义在AppConstants或专门的常量文件中。
- **资源命名**：
  - 所有资源文件（图片、颜色等）使用有意义的名称。
  - 遵循命名约定，如icon_[功能]_[状态]。

### 8. 性能与资源利用

- **内存管理**：
  - 大型集合使用懒加载。
  - 大图像正确缩放和缓存。
  - 合理使用weak引用避免内存泄漏。
  - AI模型和分析结果及时释放。
- **响应时间**：
  - UI交互响应时间控制在100ms以内。
  - 长任务使用进度指示器反馈。
  - 支付流程响应时间控制在2秒以内。
- **电池优化**：
  - 后台任务合理调度，避免频繁唤醒。
  - 网络请求批处理，减少连接次数。
  - 智能通知引擎避免过度分析。

### 9. AI代码生成指引

- **上下文提供**：
  - 向AI提供清晰的上下文信息，包括当前任务目标、相关的PRD片段、技术架构约束以及本开发规范的相关章节。
  - 明确指定要使用的架构模式（MVVM）、技术栈（SwiftUI, SwiftData, Async/Await）和项目文件结构。
- **模块化请求**：
  - 每次请求专注于单一功能点或一个可独立测试的小模块。
  - 明确说明新模块/代码与现有代码的接口（例如，ViewModel需要哪些Service，View需要哪些ViewModel）。
- **风格一致性**：
  - 生成的所有代码（包括注释）必须严格符合项目既有的代码风格、命名约定和本规范中的所有规定。
  - 遵循SwiftLint规则（如果项目配置了）。
- **文档更新责任**：AI在完成一个导致重要变更（如新组件、数据模型修改、核心逻辑实现）的开发任务后，必须按照本规范第十三章的要求，生成README.md的更新内容。确保路径符合 `Evolve/` 开头。

## 九、开发流程规范

### 1. Git工作流程

- **分支策略**：main(稳定版本)、develop(开发集成)、feature/xxx(新功能)
- **提交规范**：遵循Conventional Commits格式
- **代码审查**：功能完整性、代码质量、性能影响、安全隐患

### 2. 风险管控

- **技术风险**：SwiftData新特性、AI服务商依赖、StoreKit支付
- **性能监控**：启动时间<1.5秒、UI流畅度60fps、内存<200MB
- **兼容性**：iOS 17.0+最低支持版本

## 十、社区功能开发规范

### 1. 内容管理

- **审核机制**：关键词过滤、用户举报、AI辅助审核
- **质量保障**：鼓励真实分享、标识AI生成内容、限制营销内容

### 2. 交互设计

- **用户体验**：保持"生态隐喻"设计一致性，体现"能量流转"概念
- **AI集成**：AI作为"社区引导者"，提供个性化鼓励和分享建议

### 3. 隐私安全

- **隐私保护**：可选分享范围、匿名选项、用户数据控制
- **数据安全**：社区数据分离存储、遵循GDPR法规

### 4. 性能优化

- **加载性能**：分页加载(≤20条)、图片懒加载、本地缓存
- **用户体验**：页面加载<2秒、离线浏览、平滑滚动

## 十一、README.md 更新规范

### 1. 更新时机

AI完成以下开发任务后必须更新README.md：
- **新功能模块**：完成PRD中定义的功能模块
- **新组件创建**：UI组件、核心服务、ViewModel
- **数据模型变更**：新增或修改@Model实体
- **API集成**：新的第三方API或支付功能

### 2. 更新内容

- **日期和版本**：变更发生的日期和版本号
- **变更类型**：新增、修改、修复、移除
- **变更描述**：功能点、组件路径、数据模型变更
- **简要说明**：复杂变更的设计思路

## 技术栈
(引用技术架构文档核心技术栈)



## 项目结构概览
(引用技术架构文档项目目录结构)

## 十四、iOS开发常见崩溃与错误预防规范（简化版）

### 🚨 严重错误预防清单

**1. 调试代码清理**
- **严禁保留print()语句**：所有print()调试代码必须在提交前清理
- **移除临时代码**：删除所有#if DEBUG调试逻辑和测试代码

**2. SwiftData模型设计**
- **避免重复关系**：每个@Relationship只定义一次，使用inverse建立双向关系
- **UUID自动生成**：不要手动设置UUID，让系统自动生成
- **正确初始化**：使用EACompletion()而非EACompletion(habitId:)

**3. @MainActor线程安全**
- **ViewModel标记**：所有ViewModel必须标记@MainActor
- **UI更新主线程**：确保所有UI更新在主线程执行
- **长任务后台**：使用Task.detached处理长时间任务

**4. 内存管理**
- **闭包循环引用**：使用[weak self]防止循环引用
- **delegate弱引用**：delegate属性必须声明为weak
- **资源清理**：在deinit中清理Timer、Observer等资源

**5. iOS 17.0+兼容性**
- **onChange新语法**：使用.onChange(of: value) { }而非旧版本语法
- **避免弃用API**：不使用已弃用的StoreKit 1等API

**6. 错误处理**
- **网络请求**：实现超时和重试机制
- **支付流程**：正确处理用户取消、网络错误等情况
- **优雅降级**：提供错误恢复方案，避免应用崩溃

### 📋 开发完成检查清单

每次开发完成后必须检查：
- [ ] 清理所有print()调试语句
- [ ] 所有闭包使用[weak self]
- [ ] ViewModel标记@MainActor
- [ ] SwiftData关系正确定义
- [ ] 网络请求有错误处理
- [ ] 资源在deinit中清理
- [ ] 避免使用已弃用API

### ⚡ 快速修复指南

**遇到崩溃时的诊断步骤：**
1. 检查崩溃日志中的错误类型
2. 确认UI更新是否在主线程
3. 使用Xcode Memory Graph检测循环引用
4. 验证SwiftData模型关系定义
5. 检查网络请求错误处理

**⚠️ 重要提醒**：这些规范基于iOS 17.0+制定，随着iOS版本更新可能需要调整。开发时请关注Apple官方文档更新。

## 十五、编译错误系统性修复规范

### 🔄 禁止循环修复原则

**严禁单点修复模式**：AI在遇到编译错误时，必须采用系统性分析和一次性修复策略，避免"修复A导致B错误，修复B导致C错误"的无限循环。

### 📋 错误修复标准流程

**第一步：全面错误收集**
- 执行完整项目编译，收集所有编译错误和警告
- 识别错误类型：SwiftData关系错误、语法错误、类型冲突、重复定义、依赖缺失、@MainActor问题等
- 分析错误间的关联性和依赖关系

**第二步：根因分析**
- **SwiftData关系错误（最高优先级）**：检查inverse参数的KeyPath是否正确，属性名是否拼写准确，关系两端类型是否匹配，确保遵循单端inverse规则
- **重复定义问题**：检查是否在多个文件中定义了相同的类型、枚举或结构体
- **MainActor冲突**：识别所有需要@MainActor标记的ViewModel和相关调用
- **依赖链问题**：分析import缺失、模块依赖和文件引用关系
- **命名空间冲突**：检查EA前缀使用是否一致，是否与系统类型冲突

**第三步：制定修复计划**
- 按优先级排序：**首先解决SwiftData关系问题**，再解决基础依赖问题，最后解决上层业务逻辑问题
- 识别需要同时修改的文件组合
- 预判修复可能引发的连锁反应

**第四步：批量执行修复**
- **一次性修复所有相关文件**，而非逐个修复
- 确保修复方案的一致性和完整性
- 优先使用项目既有的命名规范和架构模式

### 🚨 SwiftData关系错误专项处理

**核心原则：遵循单端inverse规则，绝对不能删除inverse参数来"解决"编译错误**

**单端inverse规则说明**：
- **只在一端使用@Relationship(inverse:)**：双向关系只在一端定义@Relationship(inverse:)
- **另一端使用普通属性**：关系的另一端使用普通var属性，SwiftData会自动维护关系
- **避免双端@Relationship**：两端同时使用@Relationship会导致冲突和循环引用

**1. "circular reference resolving attached macro 'Relationship'"错误**

**根本原因分析**：
- 违反了单端inverse规则，两端都使用了@Relationship
- inverse参数的KeyPath路径错误或不存在
- 关系两端的类型不匹配
- 属性名拼写错误或大小写不一致
- 模型定义中缺少对应的属性

**系统性解决步骤**：
1. **检查单端inverse规则**：确保只有一端使用@Relationship(inverse:)，另一端使用普通属性
2. **精确错误定位**：明确是哪两个模型之间的哪对关系出现问题
3. **双向关系完整性核对**：
   ```swift
   // ✅ 正确的单端inverse模式
   @Model class EAUser {
       @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
       var habits: [EAHabit] = []
   }
   @Model class EAHabit {
       var user: EAUser? // 普通属性，SwiftData自动维护关系
   }
   ```
4. **KeyPath精确验证**：逐字符核对inverse参数的KeyPath是否精确指向对应属性
5. **类型匹配检查**：确认关系两端的类型完全匹配（一对一、一对多、多对多）
6. **属性名一致性验证**：检查属性名的大小写、单复数是否完全一致

**常见错误修正示例**：
```swift
// ❌ 错误：两端都使用@Relationship（违反单端inverse规则）
@Model class EAHabit {
    @Relationship(inverse: \EAUser.habits)
    var user: EAUser?
}
@Model class EAUser {
    @Relationship(inverse: \EAHabit.user) // 错误：冗余定义
    var habits: [EAHabit] = []
}

// ✅ 正确：单端inverse规则
@Model class EAUser {
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit] = []
}
@Model class EAHabit {
    var user: EAUser? // 普通属性，无需@Relationship
}

// ❌ 错误：KeyPath指向不存在的属性
@Relationship(inverse: \EAHabit.completion) // 属性名错误
var habit: EAHabit?

// ✅ 正确：KeyPath指向正确的属性
@Relationship(inverse: \EAHabit.completions) // 属性名正确
var habit: EAHabit?
```

**2. "Cannot find type 'XXX' in scope"错误**

**系统性解决方案**：
1. **模块导入检查**：确保所有模型类都在同一模块中或正确导入
2. **类名拼写验证**：检查类名拼写是否正确，包括EA前缀
3. **@Model标记确认**：确认模型类已正确标记@Model
4. **文件名一致性**：验证文件名与类名是否一致

**3. 关系不一致错误**

**系统性解决方案**：
1. **可选性匹配**：确保关系两端的可选性匹配业务逻辑
2. **删除规则验证**：验证deleteRule设置合理（.cascade, .noAction等）
3. **关系基数检查**：检查关系的基数是否正确（一对一、一对多、多对多）
4. **集合初始化**：确认集合类型关系已初始化为空数组

### 🚨 常见循环修复陷阱及解决方案

**1. MainActor循环问题**
- **错误模式**：在Preview中使用EASessionManager.shared导致MainActor错误
- **正确方案**：一次性标记所有相关ViewModel为@MainActor，并统一处理Preview中的依赖注入

**2. 重复定义循环**
- **错误模式**：发现ReminderStyle重复定义，只删除一个文件中的定义
- **正确方案**：全局搜索所有重复定义，统一保留最完整的版本，删除其他重复项

**3. 依赖导入循环**
- **错误模式**：缺少import时只在当前文件添加
- **正确方案**：检查所有相关文件的import语句，统一补充缺失的依赖

**4. SwiftData关系循环**
- **错误模式**：遇到关系错误时删除inverse参数或改为单向关系，或者违反单端inverse规则在两端都使用@Relationship
- **正确方案**：严格遵循单端inverse规则，通过修正KeyPath路径、属性名等方式解决，**坚持完整双向关系原则但只在一端使用@Relationship**

### ⚡ 修复执行要求

**修复前必须声明**：
- 明确说明发现的所有错误类型和数量
- 说明修复计划和涉及的文件范围
- 预告可能的连锁修改
- **特别标注SwiftData关系相关的修复策略**

**修复时必须遵循**：
- 同一类型错误必须一次性全部修复
- 相关文件必须同时修改，确保一致性
- 遵循项目既有的命名规范和架构模式
- **绝对不能删除inverse参数来"解决"SwiftData关系错误**

**修复后必须验证**：
- 执行完整编译验证所有错误已解决
- 确认没有引入新的编译错误
- 检查修复是否符合项目开发规范
- **特别验证SwiftData关系功能是否正常**

### 🛑 循环修复熔断机制

**触发条件**：如果同一类型错误修复超过2次仍未解决，必须：
1. 停止继续修复
2. 全面分析项目结构和依赖关系
3. 考虑采用彻底重建策略（特别是SwiftData模型）
4. 制定完整的重构方案
5. 向用户说明情况并请求指导

**🚨 SwiftData特殊熔断机制**：
- 如果SwiftData关系错误反复出现，考虑采用"彻底清理+逐个重建"策略
- 删除所有@Model文件和SwiftData物理存储
- 按照简化架构逐个重建：EAUser → EAUserSettings → EAHabit → EACompletion
- 每个模型创建后立即验证编译和关系功能

**⚠️ 重要提醒**：修复编译错误时，质量比速度更重要。宁可花时间做全面分析，也不要陷入无限循环修复。**对于SwiftData关系错误，坚持完整双向inverse关系原则是绝对不可妥协的底线。**

- **⚠️ 重要提醒**：
  - **权威资料参考**：碰到难以修复或多次修复无法解决的SwiftData问题时，请搜索全网和苹果官方开发者相关社区，寻找权威的资料
  - **Apple官方文档优先**：始终以Apple WWDC视频、官方文档和开发者论坛为准
  - **社区最佳实践**：参考Stack Overflow、GitHub上的成熟开源项目实现

## 十六、SwiftData版本兼容性开发规范（2025年修正版）

### 🎯 SwiftData关系完整性开发要求（Apple官方标准）

**强制要求（遵循Apple WWDC 2024-2025官方指导）：**
- **完整双向关系**：所有双向关系必须明确定义`@Relationship(inverse:)`
- **关系对称性**：确保关系两端都正确声明inverse参数
- **级联删除规则**：合理使用deleteRule确保数据完整性
- **关系一致性**：通过完整的关系定义确保数据一致性
- **可选性匹配**：关系的可选性根据业务逻辑合理设置

**开发检查清单：**
```swift
// ✅ 正确：完整的双向关系设计（Apple官方标准）
@Model final class EAHabit {
    var id: UUID = UUID()
    var name: String
    var creationDate: Date = Date()
    var iconName: String
    var targetFrequency: Int
    var timeOfDay: String?
    var isActive: Bool = true
    
    // ✅ 正确：完整双向关系定义
    @Relationship(deleteRule: .cascade, inverse: \EACompletion.habit) 
    var completions: [EACompletion] = []
    
    @Relationship(inverse: \EAUser.habits) 
    var user: EAUser?
    
    init(name: String, iconName: String, targetFrequency: Int, timeOfDay: String? = nil) {
        self.name = name
        self.iconName = iconName
        self.targetFrequency = targetFrequency
        self.timeOfDay = timeOfDay
    }
}

@Model final class EACompletion {
    var id: UUID = UUID()
    var date: Date = Date()
    var completionNote: String?
    var energyLevel: Int = 5 // 1-10
    
    // ✅ 正确：使用普通属性，SwiftData自动维护inverse关系
    var habit: EAHabit?
    
    init(completionNote: String? = nil, energyLevel: Int = 5) {
        self.completionNote = completionNote
        self.energyLevel = energyLevel
    }
}
```

### 🔧 SwiftData性能优化开发规范

**查询性能规范：**
- **简单@Query**：仅用于简单谓词，避免复杂逻辑
- **复杂查询**：使用@ModelActor处理复杂数据操作
- **批量操作**：使用ModelContext.transaction提高性能
- **内存管理**：及时释放大型查询结果

**实现示例：**
```swift
// ✅ 推荐：简单查询使用@Query
@Query(filter: #Predicate<EAHabit> { $0.isActive == true }) 
var activeHabits: [EAHabit]

// ✅ 复杂查询使用@ModelActor
@ModelActor
actor EADataManager {
    func fetchHabitsWithCompletions() throws -> [EAHabit] {
        let descriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { habit in
                habit.isActive == true
            },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
    
    // 通过关系查询完成记录
    func fetchCompletionsForHabit(_ habitId: UUID) throws -> [EACompletion] {
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate { completion in
                completion.habit?.id == habitId
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        return try modelContext.fetch(descriptor)
    }
}
```

### 🚨 SwiftData编译错误系统性解决方案

#### 常见编译错误及解决方案

**1. "circular reference resolving attached macro 'Relationship'"**

**原因分析**：
- inverse参数引用的属性名不存在或拼写错误
- 关系两端的类型不匹配
- 模型定义顺序问题
- KeyPath路径错误

**解决步骤**：
1. 检查inverse参数的KeyPath是否正确：`\TargetModel.propertyName`
2. 确认关系两端的类型完全匹配
3. 验证属性名拼写是否正确
4. 确保模型类都已正确定义

**示例修正**：
```swift
// ❌ 错误：KeyPath不正确
@Relationship(inverse: \EAHabit.completion) // 属性名错误
var habit: EAHabit?

// ✅ 正确：KeyPath正确
@Relationship(inverse: \EAHabit.completions) // 属性名正确
var habit: EAHabit?
```

**2. "Cannot find type 'XXX' in scope"**

**解决方案**：
1. 确保所有模型类都在同一模块中或正确导入
2. 检查类名拼写是否正确
3. 确认模型类已正确标记@Model

**3. 关系不一致错误**

**解决方案**：
1. 确保关系两端的可选性匹配业务逻辑
2. 验证deleteRule设置合理
3. 检查关系的基数是否正确（一对一、一对多、多对多）

### 📊 数据库重置和迁移策略

**开发阶段数据库重置**：
```swift
// 开发阶段：完全重置数据库
func resetDatabase() {
    do {
        // 删除现有数据库文件
        try modelContainer.deleteAllData()
        
        // 重新创建ModelContainer
        let schema = Schema([
            EAHabit.self,
            EACompletion.self,
            EAUser.self,
            EAUserSettings.self,
            EAAIMessage.self,
            EAPayment.self,
            EAAnalytics.self,
            EAContent.self,
            EAPath.self
        ])
        
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
        modelContainer = try ModelContainer(for: schema, configurations: [modelConfiguration])
        
        print("✅ 数据库重置成功")
    } catch {
        print("❌ 数据库重置失败: \(error)")
    }
}
```

**生产环境迁移策略**：
```swift
// 生产环境：版本化迁移
let schema = Schema([
    EAHabit.self,
    EACompletion.self,
    EAUser.self,
    // ... 其他模型
])

let modelConfiguration = ModelConfiguration(
    schema: schema,
    isStoredInMemoryOnly: false,
    cloudKitDatabase: .automatic // 支持CloudKit同步
)

do {
    modelContainer = try ModelContainer(
        for: schema,
        configurations: [modelConfiguration]
    )
} catch {
    print("❌ ModelContainer创建失败: \(error)")
    // 实现降级策略
}
```

### 💡 SwiftData最佳实践

1. **始终使用完整的双向关系**
2. **通过关系访问数据，而非外键查询**
3. **合理设置deleteRule确保数据完整性**
4. **使用@ModelActor处理复杂数据操作**
5. **定期验证关系的完整性和一致性**
6. **🔑 Apple官方配置最佳实践（WWDC确认）**：
   - **可变参数语法**：多模型使用可变参数语法 `for: Model1.self, Model2.self`
   - **单模型配置**：直接传递 `for: Model.self`
   - **Preview配置**：必须为使用SwiftData的Preview配置ModelContainer
   - **Schema对象配置**：使用 `for: schema, configurations: [config]` 语法
   - **内存配置**：Preview使用 `isStoredInMemoryOnly: true`
   - **iOS 18.0+兼容性**：使用可变参数语法确保兼容性
   - **数组属性支持**：SwiftData原生支持[String]、[Int]等基础类型数组，iOS 18.2+日志警告是系统级问题

### ⚠️ 重要提醒

**质量保证：**
- 每个关系都必须有对应的inverse
- 所有关系对都必须完整定义
- 删除规则必须合理设置
- 数据操作必须通过完整测试

- **关系赋值上下文一致性（iOS 18+强制要求）**：所有SwiftData关系赋值，必须保证两端对象都属于同一个ModelContext。禁止直接用SessionManager、单例、缓存等持有的模型对象参与关系赋值，必须用当前操作的modelContext根据ID重新fetch对象后再赋值。否则会导致iOS 18+下奔溃或数据异常。

**典型错误：**
```swift
let newHabit = EAHabit(...)
modelContext.insert(newHabit)
newHabit.user = EASessionManager.shared.currentUser // ❌ 跨Context风险
```

**正确做法：**
```swift
let userId = EASessionManager.shared.currentUser?.id
let descriptor = FetchDescriptor<EAUser>(predicate: #Predicate { $0.id == userId })
let currentUser = try? modelContext.fetch(descriptor).first
newHabit.user = currentUser // ✅ 上下文一致
```

- **全局单例/SessionManager/Repository等只存ID，不直接持有模型对象引用。所有业务操作都应用当前Context fetch对象。**
