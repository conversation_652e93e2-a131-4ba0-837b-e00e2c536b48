import Foundation
import SwiftData

@Model
final class EAUser: @unchecked Sendable {
    var id: UUID = UUID()
    var username: String
    var email: String?
    var creationDate: Date = Date()
    var isPro: Bool = false
    var proExpirationDate: Date?
    
    // 🔑 头像数据持久化字段
    /// 用户头像数据（序列化存储）
    var avatarDataEncoded: Data?
    
    // MARK: - 核心关系（≤5个，遵循开发规范文档）
    
    // 🔗 核心关系1：用户-设置（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSettings.user)
    var settings: EAUserSettings?
    
    // 🔗 核心关系2：用户-习惯（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAHabit.user)
    var habits: [EAHabit]
    
    // 🔗 核心关系3：用户-社交档案（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserSocialProfile.user)
    var socialProfile: EAUserSocialProfile?
    
    // 🔗 核心关系4：用户-管理档案（一对一）
    @Relationship(deleteRule: .cascade, inverse: \EAUserModerationProfile.user)
    var moderationProfile: EAUserModerationProfile?
    
    // 🔗 核心关系5：用户-数据档案（一对一）- 替换原有的posts关系
    @Relationship(deleteRule: .cascade, inverse: \EAUserDataProfile.user)
    var dataProfile: EAUserDataProfile?
    
    init(username: String, email: String? = nil) {
        self.username = username
        self.email = email
        
        // ✅ 关键修复：iOS 18.2要求关系集合在init中初始化
        self.habits = []
    }
    
    // MARK: - 头像数据管理
    
    /// 获取头像数据（计算属性）
    var avatarData: EAAvatarData? {
        get {
            guard let encodedData = avatarDataEncoded else { return nil }
            return try? JSONDecoder().decode(EAAvatarData.self, from: encodedData)
        }
        set {
            if let newAvatarData = newValue {
                avatarDataEncoded = try? JSONEncoder().encode(newAvatarData)
            } else {
                avatarDataEncoded = nil
            }
        }
    }
    
    /// 更新头像数据（安全方法）
    @MainActor
    func updateAvatarData(_ avatarData: EAAvatarData?, in context: ModelContext) throws {
        guard self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        
        // 更新头像数据
        self.avatarData = avatarData
        
        // 保存到数据库
        try context.save()
    }
    
    /// 获取头像显示名称
    func getAvatarDisplayName() -> String {
        return avatarData?.type.displayName ?? "默认头像"
    }
    
    /// 检查是否有自定义头像
    func hasCustomAvatar() -> Bool {
        return avatarData?.type == .custom && avatarData?.customImageData != nil
    }
    
    // MARK: - 核心功能便捷方法
    
    /// 获取用户发布的可见帖子数量（通过社交档案）
    func getVisiblePostsCount() -> Int {
        return socialProfile?.getVisiblePostsCount() ?? 0
    }
    
    /// 获取用户关注的人数（通过社交档案）
    func getFollowingCount() -> Int {
        return socialProfile?.getFollowingCount() ?? 0
    }
    
    /// 获取关注用户的人数（通过社交档案）
    func getFollowersCount() -> Int {
        return socialProfile?.getFollowersCount() ?? 0
    }
    
    /// 获取用户未处理的举报数量（通过管理档案）
    func getPendingReportsCount() -> Int {
        return moderationProfile?.getActiveReceivedReportsCount() ?? 0
    }
    
    /// 检查是否关注了某个用户（通过社交档案）
    func isFollowing(userId: UUID) -> Bool {
        return socialProfile?.isFollowing(userId: userId) ?? false
    }
    
    /// 检查是否被某个用户关注（通过社交档案）
    func isFollowedBy(userId: UUID) -> Bool {
        return socialProfile?.isFollowedBy(userId: userId) ?? false
    }
    
    /// 检查用户是否可以发布内容（通过管理档案）
    func canPostContent() -> Bool {
        return moderationProfile?.canPostContent() ?? true
    }
    
    /// 检查用户是否可以评论（通过管理档案）
    func canComment() -> Bool {
        return moderationProfile?.canComment() ?? true
    }
    
    /// 检查用户是否可以点赞（通过管理档案）
    func canLike() -> Bool {
        return moderationProfile?.canLike() ?? true
    }
    
    /// 计算用户的社区活跃度分数
    func getCommunityActivityScore() -> Double {
        let postsScore = Double(getVisiblePostsCount()) * 5.0
        let socialScore = socialProfile?.socialActivityScore ?? 0.0
        
        return postsScore + socialScore
    }
    
    // MARK: - 数据档案便捷访问方法
    
    /// 获取活跃支付记录数量（通过数据档案）
    func getActivePaymentsCount() -> Int {
        return dataProfile?.getActivePaymentsCount() ?? 0
    }
    
    /// 获取最近的分析记录（通过数据档案）
    func getRecentAnalytics(days: Int = 7) -> [EAAnalytics] {
        return dataProfile?.getRecentAnalytics(days: days) ?? []
    }
    
    /// 获取活跃路径数量（通过数据档案）
    func getActivePathsCount() -> Int {
        return dataProfile?.getActivePathsCount() ?? 0
    }
    
    /// 获取最近的AI消息（通过数据档案）
    func getRecentAIMessages(limit: Int = 10) -> [EAAIMessage] {
        return dataProfile?.getRecentAIMessages(limit: limit) ?? []
    }
    
    // MARK: - 安全关系操作方法（iOS 18.2兼容性）
    
    /// 安全的设置关联方法
    @MainActor
    func safelyAssignSettings(_ settings: EAUserSettings, in context: ModelContext) throws {
        guard settings.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.settings = settings
        try context.save()
    }
    
    /// 安全的习惯添加方法
    @MainActor
    func safelyAddHabit(_ habit: EAHabit, in context: ModelContext) throws {
        guard habit.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.habits.append(habit)
        try context.save()
    }
    
    /// 安全的社交档案关联方法
    @MainActor
    func safelyAssignSocialProfile(_ profile: EAUserSocialProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.socialProfile = profile
        try context.save()
    }
    
    /// 安全的管理档案关联方法
    @MainActor
    func safelyAssignModerationProfile(_ profile: EAUserModerationProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.moderationProfile = profile
        try context.save()
    }
    
    /// 安全的数据档案关联方法
    @MainActor
    func safelyAssignDataProfile(_ profile: EAUserDataProfile, in context: ModelContext) throws {
        guard profile.modelContext == context, self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        self.dataProfile = profile
        try context.save()
    }
    
    /// 创建并关联社交档案
    @MainActor
    func createSocialProfile(in context: ModelContext) throws -> EAUserSocialProfile {
        let profile = EAUserSocialProfile()
        context.insert(profile)
        try safelyAssignSocialProfile(profile, in: context)
        return profile
    }
    
    /// 创建并关联管理档案
    @MainActor
    func createModerationProfile(in context: ModelContext) throws -> EAUserModerationProfile {
        let profile = EAUserModerationProfile()
        context.insert(profile)
        try safelyAssignModerationProfile(profile, in: context)
        return profile
    }
    
    /// 创建并关联数据档案
    @MainActor
    func createDataProfile(in context: ModelContext) throws -> EAUserDataProfile {
        let profile = EAUserDataProfile()
        context.insert(profile)
        try safelyAssignDataProfile(profile, in: context)
        return profile
    }
}

// MARK: - 数据模型错误类型

enum DataModelError: Error, LocalizedError {
    case contextMismatch
    case relationshipValidationFailed
    case dataIntegrityError
    case userNotFound
    case duplicateUser(String)
    case methodDeprecated
    
    var errorDescription: String? {
        switch self {
        case .contextMismatch:
            return "数据上下文不匹配 - iOS 18.2兼容性要求"
        case .relationshipValidationFailed:
            return "关系验证失败"
        case .dataIntegrityError:
            return "数据完整性错误"
        case .userNotFound:
            return "用户未找到"
        case .duplicateUser(let message):
            return message
        case .methodDeprecated:
            return "方法已弃用，请使用Repository模式"
        }
    }
} 