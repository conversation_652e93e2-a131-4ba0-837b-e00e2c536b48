import Foundation
import SwiftData

// MARK: - SwiftData用户Repository实现
@ModelActor
actor EASwiftDataUserRepository: EAUserRepository {
    
    func fetchUser(id: UUID) async throws -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == id }
        )
        return try modelContext.fetch(descriptor).first
    }
    
    func fetchCurrentUser() async throws -> EAUser? {
        // 🔑 修复：优先返回最近创建的用户（通常是当前用户）
        let descriptor = FetchDescriptor<EAUser>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        let users = try modelContext.fetch(descriptor)
        

        
        return users.first
    }
    
    func saveUser(_ user: EAUser) async throws {
        modelContext.insert(user)
        try modelContext.save()
    }
    
    func deleteUser(_ user: EAUser) async throws {
        modelContext.delete(user)
        try modelContext.save()
    }
    
    func createUser(username: String, email: String?) async throws -> EAUser {
        let user = EAUser(username: username, email: email)
        modelContext.insert(user)
        try modelContext.save()
        return user
    }
    
    // 🔑 头像数据安全更新方法
    func updateUserAvatar(userId: UUID, avatarData: EAAvatarData?) async throws {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        
        guard let user = try modelContext.fetch(descriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        // 直接更新头像数据（使用计算属性的setter）
        user.avatarData = avatarData
        
        // 保存到数据库
        try modelContext.save()
    }
    
    // AI数据桥接所需方法
    func fetchUser(by userId: UUID) async -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        return try? modelContext.fetch(descriptor).first
    }
    
    func fetchUserSettings(userId: UUID) async -> EAUserSettings? {
        // 🔑 修复：使用两步查询避免SwiftData TERNARY表达式错误
        // 第一步：获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try? modelContext.fetch(userDescriptor).first else {
            return nil
        }
        
        // 第二步：通过用户关系获取设置
        return user.settings
    }
    
    // MARK: - 认证相关方法
    func validateCredentials(phoneNumber: String, password: String) async throws -> EAUser? {
        // 查找认证信息
        let authDescriptor = FetchDescriptor<EAUserAuthInfo>(
            predicate: #Predicate { $0.phoneNumber == phoneNumber }
        )
        let authInfos = try modelContext.fetch(authDescriptor)
        
        guard let authInfo = authInfos.first else { return nil }
        
        // 验证密码（简化版）
        guard verifyPassword(password, hash: authInfo.passwordHash) else { return nil }
        
        // 通过数据档案关系返回用户
        return authInfo.userDataProfile?.user
    }
    
    func createUserWithAuth(username: String, email: String?, phoneNumber: String, password: String) async throws -> EAUser {
        // 检查手机号是否已存在
        if try await fetchUserByPhone(phoneNumber: phoneNumber) != nil {
            throw DataModelError.duplicateUser("手机号已被注册")
        }
        
        // 检查邮箱是否已存在（如果提供了邮箱）
        if let email = email, !email.isEmpty {
            if try await fetchUserByEmail(email: email) != nil {
                throw DataModelError.duplicateUser("邮箱已被注册")
            }
        }
        
        // 创建用户及其档案
        let user = try await createUserWithProfiles(username: username, email: email)
        
        // 创建认证信息
        let authInfo = EAUserAuthInfo(
            phoneNumber: phoneNumber,
            passwordHash: hashPassword(password),
            createdAt: Date()
        )
        modelContext.insert(authInfo)
        
        // 建立认证信息与数据档案的关系
        user.dataProfile?.authInfo = authInfo
        authInfo.userDataProfile = user.dataProfile
        
        // 保存到数据库
        try modelContext.save()
        
        return user
    }
    
    // MARK: - 私有辅助方法
    func fetchUserByPhone(phoneNumber: String) async throws -> EAUser? {
        // 🔑 修复：使用两步查询避免SwiftData TERNARY表达式错误
        // 第一步：通过认证信息查找
        let authDescriptor = FetchDescriptor<EAUserAuthInfo>(
            predicate: #Predicate { $0.phoneNumber == phoneNumber }
        )
        guard let authInfo = try modelContext.fetch(authDescriptor).first else {
            return nil
        }
        
        // 第二步：通过关系获取用户
        return authInfo.userDataProfile?.user
    }
    
    func fetchUserByEmail(email: String) async throws -> EAUser? {
        let descriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { user in
                user.email == email
            }
        )
        return try modelContext.fetch(descriptor).first
    }
    
    private func createUserWithProfiles(username: String, email: String?) async throws -> EAUser {
        // 创建新用户
        let user = EAUser(username: username, email: email)
        modelContext.insert(user)
        
        // 创建所有档案
        let settings = EAUserSettings()
        modelContext.insert(settings)
        user.settings = settings
        settings.user = user
        
        let socialProfile = EAUserSocialProfile()
        modelContext.insert(socialProfile)
        user.socialProfile = socialProfile
        socialProfile.user = user
        
        let moderationProfile = EAUserModerationProfile()
        modelContext.insert(moderationProfile)
        user.moderationProfile = moderationProfile
        moderationProfile.user = user
        
        let dataProfile = EAUserDataProfile()
        modelContext.insert(dataProfile)
        user.dataProfile = dataProfile
        dataProfile.user = user
        
        return user
    }
    
    private func hashPassword(_ password: String) -> String {
        // 简化的密码哈希（生产环境应使用bcrypt或类似的强加密）
        return "hash_\(password.hashValue)_\(Date().timeIntervalSince1970)"
    }
    
    private func verifyPassword(_ password: String, hash: String) -> Bool {
        // 简化的密码验证（生产环境需要对应的验证逻辑）
        return hash.contains("\(password.hashValue)")
    }
    
    // MARK: - 测试数据管理方法
    #if DEBUG
    func deleteAllTestUsers() async throws {
        // 获取所有用户
        let descriptor = FetchDescriptor<EAUser>()
        let users = try modelContext.fetch(descriptor)
        
        // 删除所有用户（关联数据会通过deleteRule自动删除）
        for user in users {
            modelContext.delete(user)
        }
        
        // 保存变更
        try modelContext.save()
    }
    #endif
}

// MARK: - SwiftData用户设置Repository实现
@ModelActor
actor EASwiftDataUserSettingsRepository: EAUserSettingsRepository {
    
    func fetchSettings(for userID: UUID) async throws -> EAUserSettings? {
        // 🔑 修复：使用两步查询避免SwiftData TERNARY表达式错误
        // 第一步：获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return nil
        }
        
        // 第二步：通过用户关系获取设置
        return user.settings
    }
    
    func saveSettings(_ settings: EAUserSettings) async throws {
        modelContext.insert(settings)
        try modelContext.save()
    }
    
    func createDefaultSettings(for userID: UUID) async throws -> EAUserSettings {
        // ✅ 修复iOS 18.2线程安全：添加防重复创建检查
        let existingSettings = try await fetchSettings(for: userID)
        if let existing = existingSettings {
            return existing
        }
        
        // ✅ 修复iOS 18.2线程安全：在@ModelActor中重新获取用户对象，避免跨线程传递
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        // ✅ 线程安全：确保在@ModelActor中安全创建设置
        let settings = EAUserSettings()
        settings.user = user
        
        // ✅ 增强错误处理：分步操作确保线程安全
        do {
            modelContext.insert(settings)
            try modelContext.save()
            return settings
        } catch {
            // 如果保存失败，从ModelContext中删除未保存的对象
            modelContext.delete(settings)
            throw error
        }
    }
}

// MARK: - SwiftData习惯Repository实现
@ModelActor
actor EASwiftDataHabitRepository: EAHabitRepository {
    
    func fetchHabits(for userID: UUID) async throws -> [EAHabit] {
        // 🔑 修复：使用两步查询避免SwiftData TERNARY表达式错误
        // 第一步：获取用户
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        // 第二步：通过用户关系获取习惯，并排序
        let habits = user.habits
        return habits.sorted { $0.creationDate > $1.creationDate }
    }
    
    func fetchActiveHabits(for userID: UUID) async throws -> [EAHabit] {
        // ✅ 修复：SwiftData不支持复杂的可选值链式访问，改为分步查询
        // 步骤1：先获取用户对象
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        // 步骤2：查询所有活跃习惯，使用简单的属性比较
        let descriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.isActive == true },
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        let allActiveHabits = try modelContext.fetch(descriptor)
        
        // 步骤3：在内存中过滤属于该用户的习惯
        return allActiveHabits.filter { habit in
            habit.user?.id == userID
        }
    }
    
    func fetchHabit(id: UUID) async throws -> EAHabit? {
        let descriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == id }
        )
        return try modelContext.fetch(descriptor).first
    }
    
    func saveHabit(_ habit: EAHabit) async throws {
        // ✅ iOS 18.2修复：确保关系赋值安全性
        // 检查habit是否已经在当前Context中
        if habit.modelContext != modelContext {
            // 如果不在当前Context中，重新创建
            throw EARepositoryError.contextMismatch
        }
        
        modelContext.insert(habit)
        try modelContext.save()
    }
    
    /// iOS 18.2安全创建方法：完整的习惯创建流程
    func createHabitSafely(
        name: String,
        iconName: String,
        targetFrequency: Int,
        frequencyType: String,
        selectedWeekdays: [Int],
        dailyTarget: Int,
        monthlyTarget: Int,
        monthlyMode: String,
        selectedMonthlyDates: [Int],
        category: String,
        difficulty: String,
        reminderTimes: [String],
        reminderEnabled: Bool,
        preferredTimeSlot: String?,
        for userID: UUID
    ) async throws -> EAHabit {
        // ✅ 步骤1：在@ModelActor中重新获取用户对象，确保线程安全
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        // ✅ 步骤2：创建习惯对象
        let habit = EAHabit(name: name, iconName: iconName, targetFrequency: targetFrequency)
        
        // ✅ 步骤3：设置完整属性
        habit.frequencyType = frequencyType
        habit.selectedWeekdays = selectedWeekdays
        habit.dailyTarget = dailyTarget
        habit.monthlyTarget = monthlyTarget
        habit.monthlyMode = monthlyMode
        habit.selectedMonthlyDates = selectedMonthlyDates
        habit.category = category
        habit.difficulty = difficulty
        habit.reminderTimes = reminderTimes
        habit.reminderEnabled = reminderEnabled
        habit.preferredTimeSlot = preferredTimeSlot
        
        // ✅ 步骤4：插入Context（必须在关系赋值前执行）
        modelContext.insert(habit)
        
        // ✅ 步骤5：设置用户关系（iOS 18+关键要求：对象插入后才能赋值关系）
        if habit.modelContext == modelContext && user.modelContext == modelContext {
            habit.user = user
        } else {
            throw EARepositoryError.contextMismatch
        }
        
        // ✅ 步骤6：保存Context
        try modelContext.save()
        
        // ✅ 关键修复：延迟发送通知，确保SwiftData关系完全生效
        // 使用Task.sleep确保关系数据已同步到其他Context
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05秒延迟
        
        // ✅ 步骤7：验证关系已建立
        let habitId = habit.id // 提取ID避免在Predicate中引用对象
        let verificationDescriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == habitId }
        )
        if let verifiedHabit = try modelContext.fetch(verificationDescriptor).first,
           verifiedHabit.user?.id == userID {
            // 关系验证成功，发送通知
            await MainActor.run {
                NotificationCenter.default.post(
                    name: NSNotification.Name("EAHabitCreated"),
                    object: habit.id
                )
                
                // 同时发送数据变化通知
                NotificationCenter.default.post(
                    name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
                    object: habit.id
                )
            }
        } else {
            // 关系验证失败，记录错误但不阻止创建流程
            print("⚠️ 习惯创建成功，但关系验证失败，延迟发送通知")
            // 延迟发送通知，给SwiftData更多时间同步
            await MainActor.run {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("EAHabitCreated"),
                        object: habit.id
                    )
                    
                    NotificationCenter.default.post(
                        name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
                        object: habit.id
                    )
                }
            }
        }
        
        return habit
    }
    
    func deleteHabit(_ habit: EAHabit) async throws {
        modelContext.delete(habit)
        try modelContext.save()
    }
    
    func createHabit(
        name: String, 
        iconName: String, 
        targetFrequency: Int, 
        frequencyType: String,
        category: String,
        difficulty: String,
        for userID: UUID
    ) async throws -> EAHabit {
        // ✅ 修复iOS 18.2线程安全：在@ModelActor中重新获取用户对象
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw EARepositoryError.userNotFound
        }
        
        let habit = EAHabit(name: name, iconName: iconName, targetFrequency: targetFrequency)
        
        // 设置完整属性
        habit.frequencyType = frequencyType
        habit.category = category
        habit.difficulty = difficulty
        habit.user = user
        
        modelContext.insert(habit)
        try modelContext.save()
        
        // ✅ 关键修复：延迟发送通知，确保SwiftData关系完全生效
        // 使用Task.sleep确保关系数据已同步到其他Context
        try await Task.sleep(nanoseconds: 50_000_000) // 0.05秒延迟
        
        // ✅ 验证关系已建立
        let habitId = habit.id // 提取ID避免在Predicate中引用对象
        let verificationDescriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == habitId }
        )
        if let verifiedHabit = try modelContext.fetch(verificationDescriptor).first,
           verifiedHabit.user?.id == userID {
            // 关系验证成功，发送通知
            await MainActor.run {
                NotificationCenter.default.post(
                    name: NSNotification.Name("EAHabitCreated"),
                    object: habit.id
                )
                
                // 同时发送数据变化通知
                NotificationCenter.default.post(
                    name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
                    object: habit.id
                )
            }
        }
        
        return habit
    }
    
    // AI数据桥接所需方法
    func fetchUserHabits(userId: UUID) async -> [EAHabit] {
        // ✅ 修复：SwiftData不支持复杂的可选值链式访问，改为分步查询
        // 步骤1：先获取用户对象
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try? modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        // 步骤2：查询所有习惯，使用简单的属性比较
        let descriptor = FetchDescriptor<EAHabit>(
            sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
        )
        
        let allHabits = (try? modelContext.fetch(descriptor)) ?? []
        
        // 步骤3：在内存中过滤属于该用户的习惯
        return allHabits.filter { habit in
            habit.user?.id == userId
        }
    }
    
    func fetchRecentCompletions(userId: UUID, days: Int) async -> [EACompletion] {
        let startDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        
        // ✅ 修复：SwiftData不支持复杂的可选值链式访问，改为分步查询
        // 步骤1：先获取用户的所有习惯
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try? modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        let userHabitIds = user.habits.map { $0.id }
        guard !userHabitIds.isEmpty else {
            return []
        }
        
        // 步骤2：查询时间范围内的完成记录，使用简单的属性比较
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate { completion in
                completion.date >= startDate
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        let allCompletions = (try? modelContext.fetch(descriptor)) ?? []
        
        // 步骤3：在内存中过滤属于用户的完成记录
        return allCompletions.filter { completion in
            guard let habit = completion.habit else { return false }
            return userHabitIds.contains(habit.id)
        }
    }
}

// MARK: - SwiftData完成记录Repository实现
@ModelActor
actor EASwiftDataCompletionRepository: EACompletionRepository {
    
    func fetchCompletions(for habitID: UUID) async throws -> [EACompletion] {
        // 🔑 修复：使用两步查询避免SwiftData TERNARY表达式错误
        // 第一步：获取习惯对象
        let habitDescriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == habitID }
        )
        guard let habit = try modelContext.fetch(habitDescriptor).first else {
            return []
        }
        
        // 第二步：通过关系获取完成记录
        let completions = habit.completions.sorted { $0.date > $1.date }
        return completions
    }
    
    func fetchTodayCompletions(for userID: UUID) async throws -> [EACompletion] {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        // ✅ 修复：SwiftData不支持复杂的可选值链式访问，改为分步查询
        // 步骤1：先获取用户的所有习惯
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        let userHabitIds = user.habits.map { $0.id }
        guard !userHabitIds.isEmpty else {
            return []
        }
        
        // 步骤2：查询今天的完成记录，使用简单的属性比较
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate { completion in
                completion.date >= today &&
                completion.date < tomorrow
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        let allTodayCompletions = try modelContext.fetch(descriptor)
        
        // 步骤3：在内存中过滤属于用户的完成记录
        return allTodayCompletions.filter { completion in
            guard let habit = completion.habit else { return false }
            return userHabitIds.contains(habit.id)
        }
    }
    
    func fetchCompletions(for userID: UUID, date: Date) async throws -> [EACompletion] {
        let startOfDay = Calendar.current.startOfDay(for: date)
        let endOfDay = Calendar.current.date(byAdding: .day, value: 1, to: startOfDay)!
        
        // ✅ 修复：SwiftData不支持复杂的可选值链式访问，改为分步查询
        // 步骤1：先获取用户的所有习惯
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        let userHabitIds = user.habits.map { $0.id }
        guard !userHabitIds.isEmpty else {
            return []
        }
        
        // 步骤2：查询指定日期的完成记录，使用简单的属性比较
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate { completion in
                completion.date >= startOfDay &&
                completion.date < endOfDay
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        let allCompletions = try modelContext.fetch(descriptor)
        
        // 步骤3：在内存中过滤属于用户的完成记录
        return allCompletions.filter { completion in
            guard let habit = completion.habit else { return false }
            return userHabitIds.contains(habit.id)
        }
    }
    
    func saveCompletion(_ completion: EACompletion) async throws {
        modelContext.insert(completion)
        try modelContext.save()
    }
    
    func deleteCompletion(_ completion: EACompletion) async throws {
        modelContext.delete(completion)
        try modelContext.save()
    }
    
    func createCompletion(for habitID: UUID, note: String?, energyLevel: Int) async throws -> EACompletion {
        // ✅ 修复iOS 18.2线程安全：在@ModelActor中重新获取习惯对象
        let habitDescriptor = FetchDescriptor<EAHabit>(
            predicate: #Predicate { $0.id == habitID }
        )
        guard let habit = try modelContext.fetch(habitDescriptor).first else {
            throw EARepositoryError.habitNotFound
        }
        
        // ✅ 新增：Context一致性检查，防止iOS 18+崩溃
        #if DEBUG
        let currentContextId = ObjectIdentifier(modelContext)
        let habitContextId = ObjectIdentifier(habit.modelContext ?? modelContext)
        print("🔍 Context一致性检查 - 当前Context: \(currentContextId), 习惯Context: \(habitContextId)")
        
        if habit.modelContext != modelContext {
            print("⚠️ 检测到Context不一致，将在同一Context中重新获取习惯对象")
        }
        #endif
        
        // ✅ 修复关系赋值顺序：先创建和插入对象，再建立关系
        let completion = EACompletion(completionNote: note, energyLevel: energyLevel)
        modelContext.insert(completion)  // 🔑 关键修复：先插入再赋值关系
        
        // ✅ 修复：关系赋值在插入后进行，避免SwiftData关系链冲突
        // ✅ 新增：确保Context一致性的安全赋值
        if habit.modelContext == modelContext {
            completion.habit = habit
        } else {
            // 如果Context不一致，重新获取习惯对象
            let safeHabitDescriptor = FetchDescriptor<EAHabit>(
                predicate: #Predicate { $0.id == habitID }
            )
            if let safeHabit = try modelContext.fetch(safeHabitDescriptor).first {
                completion.habit = safeHabit
                #if DEBUG
                print("✅ 使用Context一致的习惯对象完成关系赋值")
                #endif
            } else {
                throw EARepositoryError.habitNotFound
            }
        }
        
        try modelContext.save()
        
        #if DEBUG
        print("✅ 习惯完成记录创建成功，Context ID: \(ObjectIdentifier(modelContext))")
        #endif
        
        return completion
    }
    
    /// ✅ 新增：获取用户最近的完成记录（用于星际能量计算）
    func fetchRecentCompletions(userId: UUID, days: Int) async throws -> [EACompletion] {
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) ?? endDate
        
        // 步骤1：先获取用户的所有习惯
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userId }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            return []
        }
        
        let userHabitIds = user.habits.map { $0.id }
        guard !userHabitIds.isEmpty else {
            return []
        }
        
        // 步骤2：查询指定时间范围的完成记录
        let descriptor = FetchDescriptor<EACompletion>(
            predicate: #Predicate { completion in
                completion.date >= startDate &&
                completion.date <= endDate
            },
            sortBy: [SortDescriptor(\.date, order: .reverse)]
        )
        
        let allCompletions = try modelContext.fetch(descriptor)
        
        // 步骤3：在内存中过滤属于用户的完成记录
        return allCompletions.filter { completion in
            guard let habit = completion.habit else { return false }
            return userHabitIds.contains(habit.id)
        }
    }
}

// MARK: - SwiftData AI消息Repository实现
@ModelActor
actor EASwiftDataAIMessageRepository: EAAIMessageRepository {
    
    func fetchMessages(for userID: UUID, limit: Int) async throws -> [EAAIMessage] {
        // 通过用户数据档案关系获取AI消息
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first,
              let dataProfile = user.dataProfile else {
            return []
        }
        
        // 获取用户的AI消息
        let messages = dataProfile.aiMessages
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(limit)
        
        return Array(messages)
    }
    
    func fetchRecentMessages(for userID: UUID) async throws -> [EAAIMessage] {
        return try await fetchMessages(for: userID, limit: 20)
    }
    
    func saveMessage(_ message: EAAIMessage) async throws {
        modelContext.insert(message)
        try modelContext.save()
    }
    
    func deleteOldMessages(for userID: UUID, keepRecent: Int) async throws {
        let allMessages = try await fetchMessages(for: userID, limit: 1000)
        let messagesToDelete = Array(allMessages.dropFirst(keepRecent))
        
        for message in messagesToDelete {
            modelContext.delete(message)
        }
        
        if !messagesToDelete.isEmpty {
            try modelContext.save()
        }
    }
    
    func createMessage(userID: UUID, userMessage: String, aiResponse: String, type: String) async throws -> EAAIMessage {
        // 获取用户数据档案
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw DataModelError.userNotFound
        }
        
        // 确保用户有数据档案
        if user.dataProfile == nil {
            let dataProfile = EAUserDataProfile()
            modelContext.insert(dataProfile)
            dataProfile.user = user
        }
        
        // 创建AI消息
        let message = EAAIMessage(userMessage: userMessage, aiResponse: aiResponse, conversationType: type)
        modelContext.insert(message)
        
        // 建立关系
        message.userDataProfile = user.dataProfile
        
        try modelContext.save()
        return message
    }
}

// MARK: - SwiftData支付Repository实现
@ModelActor
actor EASwiftDataPaymentRepository: EAPaymentRepository {
    
    func fetchPayments(for userID: UUID) async throws -> [EAPayment] {
        // 通过用户数据档案关系获取支付记录
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first,
              let dataProfile = user.dataProfile else {
            return []
        }
        
        // 获取用户的支付记录
        return dataProfile.payments.sorted { $0.purchaseDate > $1.purchaseDate }
    }
    
    func fetchActivePayments(for userID: UUID) async throws -> [EAPayment] {
        let allPayments = try await fetchPayments(for: userID)
        return allPayments.filter { $0.isActive }
    }
    
    func savePayment(_ payment: EAPayment) async throws {
        modelContext.insert(payment)
        try modelContext.save()
    }
    
    func createPayment(userID: UUID, productId: String, transactionId: String, expirationDate: Date?) async throws -> EAPayment {
        // 获取用户数据档案
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw DataModelError.userNotFound
        }
        
        // 确保用户有数据档案
        if user.dataProfile == nil {
            let dataProfile = EAUserDataProfile()
            modelContext.insert(dataProfile)
            dataProfile.user = user
        }
        
        // 创建支付记录
        let payment = EAPayment(productId: productId, transactionId: transactionId, expirationDate: expirationDate)
        modelContext.insert(payment)
        
        // 建立关系
        payment.userDataProfile = user.dataProfile
        
        try modelContext.save()
        return payment
    }
}

// MARK: - SwiftData分析Repository实现
@ModelActor
actor EASwiftDataAnalyticsRepository: EAAnalyticsRepository {
    
    func fetchAnalytics(for userID: UUID, eventType: String?) async throws -> [EAAnalytics] {
        // 通过用户数据档案关系获取分析数据
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first,
              let dataProfile = user.dataProfile else {
            return []
        }
        
        // 获取用户的分析数据
        var analytics = dataProfile.analytics.sorted { $0.timestamp > $1.timestamp }
        
        // 如果指定了事件类型，进行过滤
        if let eventType = eventType {
            analytics = analytics.filter { $0.eventType == eventType }
        }
        
        return analytics
    }
    
    func saveAnalytics(_ analytics: EAAnalytics) async throws {
        modelContext.insert(analytics)
        try modelContext.save()
    }
    
    func trackEvent(userID: UUID, eventType: String, eventData: [String: Any]) async throws {
        // 获取用户数据档案
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw DataModelError.userNotFound
        }
        
        // 确保用户有数据档案
        if user.dataProfile == nil {
            let dataProfile = EAUserDataProfile()
            modelContext.insert(dataProfile)
            dataProfile.user = user
        }
        
        // 创建分析数据
        let jsonData = try JSONSerialization.data(withJSONObject: eventData)
        let jsonString = String(data: jsonData, encoding: .utf8) ?? "{}"
        
        let analytics = EAAnalytics(eventType: eventType, eventData: jsonString)
        modelContext.insert(analytics)
        
        // 建立关系
        analytics.userDataProfile = user.dataProfile
        
        try modelContext.save()
    }
    
    func deleteOldAnalytics(for userID: UUID, olderThan: Date) async throws {
        let allAnalytics = try await fetchAnalytics(for: userID, eventType: nil)
        let oldAnalytics = allAnalytics.filter { $0.timestamp < olderThan }
        
        for analytics in oldAnalytics {
            modelContext.delete(analytics)
        }
        
        if !oldAnalytics.isEmpty {
            try modelContext.save()
        }
    }
}

// MARK: - SwiftData内容Repository实现
@ModelActor
actor EASwiftDataContentRepository: EAContentRepository {
    
    func fetchContents(type: String?, isPro: Bool?) async throws -> [EAContent] {
        let descriptor: FetchDescriptor<EAContent>
        
        if let type = type, let isPro = isPro {
            descriptor = FetchDescriptor<EAContent>(
                predicate: #Predicate { $0.contentType == type && $0.isPro == isPro },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
        } else if let type = type {
            descriptor = FetchDescriptor<EAContent>(
                predicate: #Predicate { $0.contentType == type },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
        } else if let isPro = isPro {
            descriptor = FetchDescriptor<EAContent>(
                predicate: #Predicate { $0.isPro == isPro },
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
        } else {
            descriptor = FetchDescriptor<EAContent>(
                sortBy: [SortDescriptor(\.creationDate, order: .reverse)]
            )
        }
        
        return try modelContext.fetch(descriptor)
    }
    
    func fetchContent(id: UUID) async throws -> EAContent? {
        let descriptor = FetchDescriptor<EAContent>(
            predicate: #Predicate { $0.id == id }
        )
        return try modelContext.fetch(descriptor).first
    }
    
    func saveContent(_ content: EAContent) async throws {
        modelContext.insert(content)
        try modelContext.save()
    }
    
    func createContent(title: String, content: String, type: String, isPro: Bool) async throws -> EAContent {
        let contentItem = EAContent(title: title, content: content, contentType: type, isPro: isPro)
        modelContext.insert(contentItem)
        try modelContext.save()
        return contentItem
    }
}

// MARK: - SwiftData路径Repository实现
@ModelActor
actor EASwiftDataPathRepository: EAPathRepository {
    
    func fetchPaths(for userID: UUID) async throws -> [EAPath] {
        // 通过用户数据档案关系获取路径数据
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first,
              let dataProfile = user.dataProfile else {
            return []
        }
        
        // 获取用户的路径数据
        return dataProfile.paths.sorted { $0.creationDate > $1.creationDate }
    }
    
    func fetchPath(id: UUID) async throws -> EAPath? {
        let descriptor = FetchDescriptor<EAPath>(
            predicate: #Predicate { $0.id == id }
        )
        return try modelContext.fetch(descriptor).first
    }
    
    func savePath(_ path: EAPath) async throws {
        modelContext.insert(path)
        try modelContext.save()
    }
    
    func deletePath(_ path: EAPath) async throws {
        modelContext.delete(path)
        try modelContext.save()
    }
    
    func createPath(userID: UUID, goalTitle: String, totalStages: Int) async throws -> EAPath {
        // 获取用户数据档案
        let userDescriptor = FetchDescriptor<EAUser>(
            predicate: #Predicate { $0.id == userID }
        )
        guard let user = try modelContext.fetch(userDescriptor).first else {
            throw DataModelError.userNotFound
        }
        
        // 确保用户有数据档案
        if user.dataProfile == nil {
            let dataProfile = EAUserDataProfile()
            modelContext.insert(dataProfile)
            dataProfile.user = user
        }
        
        // 创建路径
        let path = EAPath(goalTitle: goalTitle, totalStages: totalStages)
        modelContext.insert(path)
        
        // 建立关系
        path.userDataProfile = user.dataProfile
        
        try modelContext.save()
        return path
    }
} 