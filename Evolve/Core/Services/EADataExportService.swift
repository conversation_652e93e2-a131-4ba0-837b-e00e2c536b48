import Foundation
import SwiftData

/// 数据导出错误类型
enum EADataExportError: LocalizedError {
    case exportFailed(String)
    case fileCreationFailed
    case dataEncodingFailed
    
    var errorDescription: String? {
        switch self {
        case .exportFailed(let message):
            return "导出失败：\(message)"
        case .fileCreationFailed:
            return "文件创建失败"
        case .dataEncodingFailed:
            return "数据编码失败"
        }
    }
}

/// 数据导出服务
/// 负责导出用户的习惯数据、完成记录等信息
@MainActor
class EADataExportService {
    private let repositoryContainer: EARepositoryContainer?
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    /// 主构造器（Repository模式）
    /// - Parameters:
    ///   - sessionManager: 会话管理器
    ///   - repositoryContainer: Repository容器，用于数据访问
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
    }
    
    /// 默认构造器（用于依赖注入）
    /// - Parameter sessionManager: 会话管理器
    init(sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
        self.repositoryContainer = nil
    }
    
    // MARK: - 导出所有数据
    
    /// 导出用户的所有数据
    /// - Returns: 导出文件的URL
    /// - Throws: EADataExportError 当导出过程中发生错误时
    func exportAllData() async throws -> URL {
        do {
            var exportData = EAUserDataExport()
            
            // 收集习惯数据
            exportData.habits = await exportHabits()
            
            // 收集完成记录
            exportData.completionRecords = try await exportCompletionRecords()
            
            // 收集用户资料（如果存在）
            exportData.userProfile = await exportUserProfile()
            
            // 生成JSON文件
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            
            let jsonData = try encoder.encode(exportData)
            
            // 保存到临时文件
            let fileName = "evolve_data_export_\(Date().timeIntervalSince1970).json"
            let fileURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
            
            try jsonData.write(to: fileURL)
            
            return fileURL
        } catch {
            throw EADataExportError.exportFailed(error.localizedDescription)
        }
    }
    
    // MARK: - 导出习惯数据
    
    /// 导出用户的习惯数据
    /// - Returns: 习惯导出数据数组
    private func exportHabits() async -> [EAHabitExport] {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = await sessionManager.currentUser else {
            return []
        }
        
        // 使用Repository获取数据（如果可用）
        if let container = repositoryContainer {
            let habits = await container.habitRepository.fetchUserHabits(userId: currentUser.id)
            return habits.map { habit in
                EAHabitExport(
                    id: habit.id.uuidString,
                    name: habit.name,
                    iconName: habit.iconName,
                    category: habit.category,
                    targetFrequency: habit.targetFrequency,
                    frequencyType: habit.frequencyType,
                    isActive: habit.isActive,
                    creationDate: habit.creationDate,
                    preferredTimeSlot: habit.preferredTimeSlot
                )
            }
        } else {
            // 降级处理：通过关系获取数据
            let habits = currentUser.habits
            return habits.map { habit in
                EAHabitExport(
                    id: habit.id.uuidString,
                    name: habit.name,
                    iconName: habit.iconName,
                    category: habit.category,
                    targetFrequency: habit.targetFrequency,
                    frequencyType: habit.frequencyType,
                    isActive: habit.isActive,
                    creationDate: habit.creationDate,
                    preferredTimeSlot: habit.preferredTimeSlot
                )
            }
        }
    }
    
    // MARK: - 导出完成记录
    
    /// 导出用户的完成记录
    /// - Returns: 完成记录导出数据数组
    private func exportCompletionRecords() async throws -> [EACompletionRecordExport] {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = await sessionManager.currentUser else {
            return []
        }
        
        // 使用Repository获取数据（如果可用）
        if let container = repositoryContainer {
            // 获取用户所有习惯的完成记录
            var allRecords: [EACompletion] = []
            let habits = await container.habitRepository.fetchUserHabits(userId: currentUser.id)
            
            for habit in habits {
                let completions = try await container.completionRepository.fetchCompletions(for: habit.id)
                allRecords.append(contentsOf: completions)
            }
            
            return allRecords.map { record in
                let habitName = record.habit?.name ?? "未知习惯"
                let habitId = record.habit?.id.uuidString ?? ""
                
                return EACompletionRecordExport(
                    id: record.id.uuidString,
                    habitId: habitId,
                    habitName: habitName,
                    date: record.date,
                    completionNote: record.completionNote
                )
            }
        } else {
            // 降级处理：通过关系获取数据
            var allRecords: [EACompletion] = []
            for habit in currentUser.habits {
                allRecords.append(contentsOf: habit.completions)
            }
            
            return allRecords.map { record in
                let habitName = record.habit?.name ?? "未知习惯"
                let habitId = record.habit?.id.uuidString ?? ""
                
                return EACompletionRecordExport(
                    id: record.id.uuidString,
                    habitId: habitId,
                    habitName: habitName,
                    date: record.date,
                    completionNote: record.completionNote
                )
            }
        }
    }
    
    // MARK: - 导出用户资料
    
    /// 导出用户资料
    /// - Returns: 用户资料导出数据（可选）
    private func exportUserProfile() async -> EAUserProfileExport? {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = await sessionManager.currentUser else {
            return nil
        }
        
        // 使用Repository获取数据（如果可用）
        if let container = repositoryContainer {
            let userSettings = await container.userRepository.fetchUserSettings(userId: currentUser.id)
            let preferredCoachStyle = userSettings?.preferredCoachStyle ?? "温柔鼓励型"
            
            return EAUserProfileExport(
                id: currentUser.id.uuidString,
                username: currentUser.username,
                email: currentUser.email,
                creationDate: currentUser.creationDate,
                isPro: currentUser.isPro,
                proExpirationDate: currentUser.proExpirationDate,
                preferredCoachStyle: preferredCoachStyle
            )
        } else {
            // 降级处理：通过关系获取数据
            let preferredCoachStyle = currentUser.settings?.preferredCoachStyle ?? "温柔鼓励型"
            
            return EAUserProfileExport(
                id: currentUser.id.uuidString,
                username: currentUser.username,
                email: currentUser.email,
                creationDate: currentUser.creationDate,
                isPro: currentUser.isPro,
                proExpirationDate: currentUser.proExpirationDate,
                preferredCoachStyle: preferredCoachStyle
            )
        }
    }
    
    // MARK: - 导出习惯数据（单独方法）
    
    /// 仅导出习惯数据
    /// - Returns: 习惯数据文件URL
    func exportHabitsOnly() async throws -> URL {
        let habits = await exportHabits()
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        let jsonData = try encoder.encode(habits)
        
        let fileName = "evolve_habits_export_\(Date().timeIntervalSince1970).json"
        let fileURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        try jsonData.write(to: fileURL)
        
        return fileURL
    }
}

// MARK: - 导出数据结构

/// 用户数据导出主结构
struct EAUserDataExport: Codable {
    var habits: [EAHabitExport] = []
    var completionRecords: [EACompletionRecordExport] = []
    var userProfile: EAUserProfileExport?
    var exportDate: Date = Date()
    var version: String = "1.0"
    var appVersion: String = "1.0.0"
}

/// 习惯导出数据结构
struct EAHabitExport: Codable {
    let id: String
    let name: String
    let iconName: String
    let category: String
    let targetFrequency: Int
    let frequencyType: String
    let isActive: Bool
    let creationDate: Date
    let preferredTimeSlot: String?
}

/// 完成记录导出数据结构
struct EACompletionRecordExport: Codable {
    let id: String
    let habitId: String
    let habitName: String
    let date: Date
    let completionNote: String?
}

/// 用户资料导出数据结构
struct EAUserProfileExport: Codable {
    let id: String
    let username: String
    let email: String?
    let creationDate: Date
    let isPro: Bool
    let proExpirationDate: Date?
    let preferredCoachStyle: String
} 