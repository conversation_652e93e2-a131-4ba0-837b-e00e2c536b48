//
//  EAPersonalizedRecommendationEngine.swift
//  Evolve
//
//  Created by AI Assistant on 2025-06-06.
//  Phase 2 Day 4: AI增强功能开发 - 个性化内容推荐引擎
//

import Foundation
import SwiftUI

/// 个性化内容推荐引擎
/// 基于用户兴趣标签、社交关系和行为模式，实现智能内容推荐
/// 遵循开发规范文档的"AI成本控制开发规范"和7天缓存策略
@MainActor
class EAPersonalizedRecommendationEngine: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var recommendedPosts: [EACommunityPost] = []
    @Published var recommendedUsers: [EAUser] = []
    @Published var isLoading = false
    @Published var lastUpdateTime: Date?
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let aiDataBridge: EACommunityAIDataBridge
    
    // MARK: - 缓存管理
    
    private var recommendationCache: [UUID: (recommendations: EARecommendationResult, timestamp: Date)] = [:]
    private let cacheValidDuration: TimeInterval = 7 * 24 * 3600 // 7天缓存
    private let hotContentCacheValidDuration: TimeInterval = 24 * 3600 // 热门内容24小时缓存
    
    // MARK: - 推荐算法配置
    
    private let interestWeights: [String: Double] = [
        "habit_completion": 1.0,
        "milestone_sharing": 0.9,
        "community_interaction": 0.8,
        "motivation": 0.7,
        "reflection": 0.6
    ]
    
    private let socialConnectionWeight: Double = 0.8
    private let contentFreshnessWeight: Double = 0.3
    private let popularityWeight: Double = 0.5
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer, aiDataBridge: EACommunityAIDataBridge) {
        self.repositoryContainer = repositoryContainer
        self.aiDataBridge = aiDataBridge
    }
    
    // MARK: - 个性化推荐接口
    
    /// 获取用户的个性化内容推荐
    /// - Parameter userId: 用户ID
    func generatePersonalizedRecommendations(for userId: UUID) async {
        // 检查缓存
        if let cached = recommendationCache[userId],
           Date().timeIntervalSince(cached.timestamp) < cacheValidDuration {
            updateUIWithCachedRecommendations(cached.recommendations)
            return
        }
        
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 步骤1：获取用户推荐上下文
            let recommendationContext = try await aiDataBridge.getContentRecommendationData(userId: userId)
            
            // 步骤2：执行多维度推荐算法
            let contentRecommendations = await generateContentRecommendations(context: recommendationContext)
            let userRecommendations = await generateUserRecommendations(context: recommendationContext)
            
            // 步骤3：合并和排序推荐结果
            let finalRecommendations = EARecommendationResult(
                posts: contentRecommendations,
                users: userRecommendations,
                generatedAt: Date()
            )
            
            // 步骤4：更新缓存和UI
            recommendationCache[userId] = (finalRecommendations, Date())
            updateUIWithRecommendations(finalRecommendations)
            lastUpdateTime = Date()
            
        } catch {
            // 记录错误但不中断流程
            await fallbackToHotContent()
        }
    }
    
    /// 强制刷新推荐内容
    /// - Parameter userId: 用户ID
    func refreshRecommendations(for userId: UUID) async {
        // 清除缓存
        recommendationCache.removeValue(forKey: userId)
        
        // 重新生成推荐
        await generatePersonalizedRecommendations(for: userId)
    }
    
    // MARK: - 内容推荐算法
    
    /// 生成个性化内容推荐
    private func generateContentRecommendations(context: EAAIRecommendationContext) async -> [EACommunityPost] {
        do {
            // 获取所有可用内容
            let allPosts = try await repositoryContainer.communityRepository.fetchPosts(limit: 100, offset: 0)
            
            // 过滤用户自己的帖子
            let othersPosts = allPosts.filter { $0.getAuthor()?.id != context.userId }
            
            // 计算每个帖子的推荐分数
            var scoredPosts: [(post: EACommunityPost, score: Double)] = []
            
            for post in othersPosts {
                let score = calculateContentRecommendationScore(post: post, context: context)
                scoredPosts.append((post, score))
            }
            
            // 按分数排序并返回前20个
            let sortedPosts = scoredPosts.sorted { $0.score > $1.score }
            return Array(sortedPosts.prefix(20).map { $0.post })
            
        } catch {
            // 记录错误但不中断流程
            return []
        }
    }
    
    /// 计算内容推荐分数
    private func calculateContentRecommendationScore(post: EACommunityPost, context: EAAIRecommendationContext) -> Double {
        var score: Double = 0.0
        
        // 1. 兴趣匹配分数
        let interestScore = calculateInterestMatchScore(post: post, context: context)
        score += interestScore * 0.4
        
        // 2. 社交连接分数
        let socialScore = calculateSocialConnectionScore(post: post, context: context)
        score += socialScore * 0.3
        
        // 3. 内容流行度分数
        let popularityScore = calculatePopularityScore(post: post)
        score += popularityScore * 0.2
        
        // 4. 内容新鲜度分数
        let freshnessScore = calculateFreshnessScore(post: post)
        score += freshnessScore * 0.1
        
        return score
    }
    
    /// 计算兴趣匹配分数
    private func calculateInterestMatchScore(post: EACommunityPost, context: EAAIRecommendationContext) -> Double {
        var matchScore: Double = 0.0
        
        // 检查帖子分类是否匹配用户兴趣
        if context.interests.contains(post.category) {
            matchScore += 0.5
        }
        
        // 检查帖子标签是否匹配用户偏好
        let tagMatches = post.tags.filter { context.contentPreferences.contains($0) }
        matchScore += Double(tagMatches.count) / Double(max(post.tags.count, 1)) * 0.3
        
        // 检查星际分类匹配（如果存在）
        if let stellarCategory = post.stellarCategory,
           context.interests.contains(stellarCategory) {
            matchScore += 0.2
        }
        
        return min(matchScore, 1.0)
    }
    
    /// 计算社交连接分数
    private func calculateSocialConnectionScore(post: EACommunityPost, context: EAAIRecommendationContext) -> Double {
        guard let authorId = post.getAuthor()?.id else { return 0.0 }
        
        // 检查是否关注了作者
        if context.socialConnections.contains(authorId) {
            return 1.0
        }
        
        // 检查是否有共同关注的人（二度连接）
        // 这里简化处理，实际项目中会查询共同关注关系
        return 0.1
    }
    
    /// 计算内容流行度分数
    private func calculatePopularityScore(post: EACommunityPost) -> Double {
        let totalInteractions = post.likeCount + post.commentCount * 2 + post.shareCount * 3
        
        // 归一化处理，假设100为高人气阈值
        return min(Double(totalInteractions) / 100.0, 1.0)
    }
    
    /// 计算内容新鲜度分数
    private func calculateFreshnessScore(post: EACommunityPost) -> Double {
        let hoursAgo = Date().timeIntervalSince(post.creationDate) / 3600
        
        // 24小时内的内容新鲜度最高，逐渐递减
        if hoursAgo <= 24 {
            return 1.0
        } else if hoursAgo <= 72 {
            return 0.7
        } else if hoursAgo <= 168 { // 一周
            return 0.4
        } else {
            return 0.1
        }
    }
    
    // MARK: - 用户推荐算法
    
    /// 生成个性化用户推荐
    private func generateUserRecommendations(context: EAAIRecommendationContext) async -> [EAUser] {
        do {
            // 获取潜在推荐用户（暂时简化实现）
            let allUsers = try await fetchPotentialUsers(excludingUserId: context.userId)
            
            // 计算每个用户的推荐分数
            var scoredUsers: [(user: EAUser, score: Double)] = []
            
            for user in allUsers {
                let score = calculateUserRecommendationScore(user: user, context: context)
                scoredUsers.append((user, score))
            }
            
            // 按分数排序并返回前10个
            let sortedUsers = scoredUsers.sorted { $0.score > $1.score }
            return Array(sortedUsers.prefix(10).map { $0.user })
            
        } catch {
            // 记录错误但不中断流程
            return []
        }
    }
    
    /// 获取潜在推荐用户
    private func fetchPotentialUsers(excludingUserId: UUID) async throws -> [EAUser] {
        // 这里简化实现，实际项目中会有更复杂的用户筛选逻辑
        // 可以基于共同兴趣、星际等级、活跃度等筛选
        
        // 暂时返回空数组，避免复杂的用户查询逻辑
        return []
    }
    
    /// 计算用户推荐分数
    private func calculateUserRecommendationScore(user: EAUser, context: EAAIRecommendationContext) -> Double {
        var score: Double = 0.0
        
        // 1. 星际等级相似度
        if let userLevel = user.socialProfile?.stellarLevel {
            let levelDiff = abs(userLevel - context.stellarLevel)
            let levelScore = max(0.0, 1.0 - Double(levelDiff) / 10.0)
            score += levelScore * 0.3
        }
        
        // 2. 活跃度分数
        let activityScore = calculateUserActivityScore(user: user)
        score += activityScore * 0.4
        
        // 3. 内容质量分数
        let contentQualityScore = calculateUserContentQualityScore(user: user)
        score += contentQualityScore * 0.3
        
        return score
    }
    
    /// 计算用户活跃度分数
    private func calculateUserActivityScore(user: EAUser) -> Double {
        guard let socialProfile = user.socialProfile else { return 0.0 }
        
        // 基于社交活跃度分数
        return min(socialProfile.socialActivityScore / 100.0, 1.0)
    }
    
    /// 计算用户内容质量分数
    private func calculateUserContentQualityScore(user: EAUser) -> Double {
        // 基于用户发帖数量和质量
        let postsCount = user.getVisiblePostsCount()
        return min(Double(postsCount) / 20.0, 1.0)
    }
    
    // MARK: - 降级策略
    
    /// 降级到热门内容推荐
    private func fallbackToHotContent() async {
        do {
            // 获取热门内容（按互动数排序）
            let hotPosts = try await repositoryContainer.communityRepository.fetchPosts(limit: 20, offset: 0)
            let sortedPosts = hotPosts.sorted { 
                ($0.likeCount + $0.commentCount) > ($1.likeCount + $1.commentCount) 
            }
            
            recommendedPosts = Array(sortedPosts.prefix(10))
            recommendedUsers = []
            lastUpdateTime = Date()
            
        } catch {
            // 记录错误但不中断流程
            recommendedPosts = []
            recommendedUsers = []
        }
    }
    
    // MARK: - UI更新方法
    
    /// 使用缓存推荐更新UI
    private func updateUIWithCachedRecommendations(_ recommendations: EARecommendationResult) {
        recommendedPosts = recommendations.posts
        recommendedUsers = recommendations.users
        lastUpdateTime = recommendations.generatedAt
    }
    
    /// 使用新推荐更新UI
    private func updateUIWithRecommendations(_ recommendations: EARecommendationResult) {
        recommendedPosts = recommendations.posts
        recommendedUsers = recommendations.users
    }
    
    // MARK: - 缓存清理
    
    /// 清理过期缓存
    func cleanExpiredCache() {
        let now = Date()
        recommendationCache = recommendationCache.filter { _, value in
            now.timeIntervalSince(value.timestamp) < cacheValidDuration
        }
    }
}

// MARK: - 推荐结果数据模型

/// 推荐结果数据结构
struct EARecommendationResult {
    let posts: [EACommunityPost]
    let users: [EAUser]
    let generatedAt: Date
}

/// 推荐类型
enum EARecommendationType {
    case interest       // 基于兴趣
    case social         // 基于社交关系
    case trending       // 热门趋势
    case similar        // 相似用户
    case recent         // 最新内容
}

/// 推荐项目协议
protocol EARecommendationItem {
    var id: UUID { get }
    var recommendationType: EARecommendationType { get }
    var score: Double { get }
    var reason: String { get }
} 