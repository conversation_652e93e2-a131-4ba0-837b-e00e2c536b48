import SwiftData
import Foundation

/// 设置管理服务
/// 负责管理用户设置数据的持久化存储和读取
@MainActor
final class EASettingsService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var notificationsEnabled: Bool = true
    @Published var preferredReminderTimes: [String] = ["09:00", "18:00"]
    @Published var smartTimingEnabled: Bool = true
    @Published var reminderStyle: String = "gentle"
    @Published var preferredCoachStyle: String = "温柔鼓励型"
    @Published var dataAnalyticsEnabled = true
    @Published var crashReportingEnabled = true
    
    // MARK: - Dependencies
    private let repositoryContainer: EARepositoryContainer?
    
    /// ✅ 修复：添加sessionManager依赖注入
    private let sessionManager: EASessionManager
    
    private var currentSettings: EAUserSettings?
    
    // MARK: - Initialization
    
    /// ✅ 修复：添加sessionManager依赖注入
    /// - Parameters:
    ///   - sessionManager: 会话管理器
    ///   - repositoryContainer: Repository容器，用于数据访问
    init(sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer? = nil) {
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
        loadSettings()
    }
    
    // MARK: - Public Methods
    
    /// 加载用户设置
    func loadSettings() {
        // 获取当前用户的设置
        if let userSettings = getCurrentUserSettings() {
            currentSettings = userSettings
            updatePublishedProperties(from: userSettings)
        } else {
            // 如果没有设置，创建默认设置
            Task {
                await createDefaultSettings()
            }
        }
    }
    
    /// 保存设置
    func saveSettings() async {
        do {
            if let _ = currentSettings {
                // 更新现有设置
                updateSettingsModel()
            } else {
                // 创建新设置
                await createDefaultSettings()
            }
        } catch {
            // 错误处理
        }
    }
    
    /// 更新通知设置
    func updateNotificationsEnabled(_ enabled: Bool) {
        notificationsEnabled = enabled
        updateSettingsModel()
    }
    
    /// 更新智能时间设置
    func updateSmartTimingEnabled(_ enabled: Bool) {
        smartTimingEnabled = enabled
        updateSettingsModel()
    }
    
    /// 更新提醒风格
    func updateReminderStyle(_ style: String) {
        reminderStyle = style
        updateSettingsModel()
    }
    
    /// 更新数据分析设置
    func updateDataAnalytics(enabled: Bool) async {
        dataAnalyticsEnabled = enabled
        await saveSettings()
    }
    
    /// 更新崩溃报告设置
    func updateCrashReporting(enabled: Bool) async {
        crashReportingEnabled = enabled
        await saveSettings()
    }
    
    // MARK: - Private Methods
    
    private func getCurrentUserSettings() -> EAUserSettings? {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else {
            return nil
        }
        
        // 通过关系获取用户设置
        return currentUser.settings
    }
    
    private func createDefaultSettings() async {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else {
            return
        }
        
        let defaultSettings = EAUserSettings()
        defaultSettings.user = currentUser
        
        // 使用Repository保存（如果可用）
        if let container = repositoryContainer {
            do {
                // 直接设置关系，Repository会处理保存
                currentUser.settings = defaultSettings
                currentSettings = defaultSettings
                updatePublishedProperties(from: defaultSettings)
            } catch {
                // 错误处理
            }
        } else {
            // 降级处理：直接设置关系
            currentUser.settings = defaultSettings
            currentSettings = defaultSettings
            updatePublishedProperties(from: defaultSettings)
        }
    }
    
    /// 从数据模型更新Published属性
    private func updatePublishedProperties(from settings: EAUserSettings) {
        notificationsEnabled = settings.notificationsEnabled
        preferredReminderTimes = settings.preferredReminderTimes
        smartTimingEnabled = settings.smartTimingEnabled
        reminderStyle = settings.reminderStyle
        preferredCoachStyle = settings.preferredCoachStyle
        
        // 这些设置暂时使用默认值，后续可以扩展EAUserSettings模型
        dataAnalyticsEnabled = true
        crashReportingEnabled = true
    }
    
    /// 更新数据模型
    private func updateSettingsModel() {
        guard let settings = currentSettings else { return }
        
        settings.notificationsEnabled = notificationsEnabled
        settings.preferredReminderTimes = preferredReminderTimes
        settings.smartTimingEnabled = smartTimingEnabled
        settings.reminderStyle = reminderStyle
        settings.preferredCoachStyle = preferredCoachStyle
        
        // 使用Repository保存（如果可用）
        if let container = repositoryContainer,
           let currentUser = sessionManager.currentUser {
            Task {
        do {
                    // 使用正确的Repository方法保存设置
                    try await container.userSettingsRepository.saveSettings(settings)
        } catch {
                    // 错误处理
                }
            }
        }
    }
} 