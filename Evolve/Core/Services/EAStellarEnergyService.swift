import Foundation
import SwiftUI

// MARK: - 星际能量计算引擎（Phase 3 Day 6新增）

/// 星际能量服务：负责能量计算、等级升级、成就徽章管理
@MainActor
class EAStellarEnergyService: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var isCalculating = false
    @Published var energyStatistics = EAEnergyStatistics()
    @Published var recentLevelUpEvent: EALevelUpEvent?
    @Published var recentBadgeEarned: EABadgeEarned?
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let cacheManager: EAAICacheManager
    
    /// 获取repositoryContainer的访问器（供外部组件使用）
    var repositoryContainerReference: EARepositoryContainer {
        return repositoryContainer
    }
    
    // MARK: - 配置
    
    private let energyConfig = EAStellarEnergyConfig()
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer, cacheManager: EAAICacheManager) {
        self.repositoryContainer = repositoryContainer
        self.cacheManager = cacheManager
    }
    
    // MARK: - 星际能量计算引擎
    
    /// 计算习惯完成的星际能量奖励
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - isConsecutive: 是否为连续完成
    /// - Returns: 获得的星际能量值
    func calculateHabitCompletionEnergy(habitId: UUID, userId: UUID, isConsecutive: Bool = false) async -> Int {
        guard !isCalculating else { return 0 }
        
        isCalculating = true
        defer { isCalculating = false }
        
        // 获取习惯信息
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        let userHabits = await repositoryContainer.habitRepository.fetchUserHabits(userId: userId)
        let targetHabit = userHabits.first { $0.id == habitId }
        
        guard let habit = targetHabit else { return 0 }
        
        // 基础能量奖励
        var baseEnergy = energyConfig.baseHabitCompletionEnergy
        
        // 根据习惯难度调整
        let difficultyMultiplier = calculateDifficultyMultiplier(for: habit)
        baseEnergy = Int(Double(baseEnergy) * difficultyMultiplier)
        
        // 连续完成奖励
        if isConsecutive {
            let streak = await calculateCurrentStreak(habitId: habitId, userId: userId)
            let streakBonus = calculateStreakBonus(streak: streak)
            baseEnergy += streakBonus
            
            // 记录连击成就
            await checkStreakBadges(userId: userId, habitId: habitId, streak: streak)
        }
        
        // 用户等级影响
        let userLevel = user?.socialProfile?.stellarLevel ?? 1
        let levelBonus = calculateLevelBonus(userLevel: userLevel)
        baseEnergy += levelBonus
        
        // 更新用户星际能量
        await updateUserStellarEnergy(userId: userId, energyGained: baseEnergy)
        
        // 检查等级升级
        await checkLevelUpgrade(userId: userId)
        
        // 更新统计
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.habitsCompleted += 1
        
        return baseEnergy
    }
    
    /// 计算社区分享的星际能量奖励
    /// - Parameters:
    ///   - postId: 帖子ID
    ///   - userId: 用户ID
    ///   - shareType: 分享类型
    /// - Returns: 获得的星际能量值
    func calculateSharingEnergy(postId: UUID, userId: UUID, shareType: EASharingType) async -> Int {
        let baseEnergy: Int
        
        switch shareType {
        case .habitMilestone:
            baseEnergy = energyConfig.milestoneShareEnergy
        case .reflection:
            baseEnergy = energyConfig.reflectionShareEnergy
        case .achievement:
            baseEnergy = energyConfig.achievementShareEnergy
        case .motivation:
            baseEnergy = energyConfig.motivationShareEnergy
        }
        
        // 更新用户星际能量
        await updateUserStellarEnergy(userId: userId, energyGained: baseEnergy)
        
        // 检查分享成就
        await checkSharingBadges(userId: userId, shareType: shareType)
        
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.postsShared += 1
        
        return baseEnergy
    }
    
    /// 计算社区互动的星际能量奖励
    /// - Parameters:
    ///   - interactionType: 互动类型
    ///   - userId: 用户ID
    /// - Returns: 获得的星际能量值
    func calculateInteractionEnergy(interactionType: EAInteractionType, userId: UUID) async -> Int {
        let baseEnergy: Int
        
        switch interactionType {
        case .like:
            baseEnergy = energyConfig.likeEnergy
        case .comment:
            baseEnergy = energyConfig.commentEnergy
        case .share:
            baseEnergy = energyConfig.shareEnergy
        case .follow:
            baseEnergy = energyConfig.followEnergy
        }
        
        // 更新用户星际能量
        await updateUserStellarEnergy(userId: userId, energyGained: baseEnergy)
        
        // 检查互动成就
        await checkInteractionBadges(userId: userId, interactionType: interactionType)
        
        energyStatistics.totalEnergyEarned += baseEnergy
        energyStatistics.interactionsCount += 1
        
        return baseEnergy
    }
    
    // MARK: - 用户等级自动升级逻辑
    
    /// 检查并执行用户等级升级
    /// - Parameter userId: 用户ID
    private func checkLevelUpgrade(userId: UUID) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        
        let newLevel = calculateStellarLevel(totalEnergy: totalEnergy)
        
        if newLevel > currentLevel {
            // 执行等级升级
            await upgradeUserLevel(userId: userId, newLevel: newLevel, oldLevel: currentLevel)
        }
    }
    
    /// 计算星际等级
    /// - Parameter totalEnergy: 总星际能量
    /// - Returns: 对应的星际等级
    private func calculateStellarLevel(totalEnergy: Int) -> Int {
        switch totalEnergy {
        case 0..<1000:
            return min(3, max(1, totalEnergy / 333 + 1)) // 等级1-3：新手探索者
        case 1000..<5000:
            return min(6, 4 + (totalEnergy - 1000) / 1333) // 等级4-6：星际旅者
        case 5000...:
            return min(10, 7 + (totalEnergy - 5000) / 2500) // 等级7-10：宇宙领航员
        default:
            return 1
        }
    }
    
    /// 执行用户等级升级
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - newLevel: 新等级
    ///   - oldLevel: 旧等级
    private func upgradeUserLevel(userId: UUID, newLevel: Int, oldLevel: Int) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        // 更新等级和称号
        socialProfile.stellarLevel = newLevel
        socialProfile.explorerTitle = getStellarTitle(level: newLevel)
        
        // 生成升级事件
        let levelUpEvent = EALevelUpEvent(
            userId: userId,
            oldLevel: oldLevel,
            newLevel: newLevel,
            newTitle: getStellarTitle(level: newLevel),
            timestamp: Date()
        )
        
        recentLevelUpEvent = levelUpEvent
        
        // 等级升级奖励
        let upgradeBonus = energyConfig.levelUpBonus * newLevel
        await updateUserStellarEnergy(userId: userId, energyGained: upgradeBonus)
        
        // 检查等级成就徽章
        await checkLevelBadges(userId: userId, level: newLevel)
        
        energyStatistics.levelsGained += 1
    }
    
    /// 获取星际等级称号
    /// - Parameter level: 等级
    /// - Returns: 对应的称号
    private func getStellarTitle(level: Int) -> String {
        switch level {
        case 1...3:
            return "新手探索者"
        case 4...6:
            return "星际旅者"
        case 7...10:
            return "宇宙领航员"
        default:
            return "传奇探索者"
        }
    }
    
    // MARK: - 成就徽章系统
    
    /// 检查习惯连击成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - habitId: 习惯ID
    ///   - streak: 连击天数
    private func checkStreakBadges(userId: UUID, habitId: UUID, streak: Int) async {
        let badgeType: EABadgeType?
        
        switch streak {
        case 7:
            badgeType = .weekStreak
        case 30:
            badgeType = .monthStreak
        case 100:
            badgeType = .hundredDayStreak
        case 365:
            badgeType = .yearStreak
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "习惯连击\(streak)天")
        }
    }
    
    /// 检查分享成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - shareType: 分享类型
    private func checkSharingBadges(userId: UUID, shareType: EASharingType) async {
        let totalShares = energyStatistics.postsShared
        
        let badgeType: EABadgeType?
        switch totalShares {
        case 10:
            badgeType = .sharingNovice
        case 50:
            badgeType = .sharingExpert
        case 100:
            badgeType = .sharingMaster
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "分享达人")
        }
    }
    
    /// 检查互动成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - interactionType: 互动类型
    private func checkInteractionBadges(userId: UUID, interactionType: EAInteractionType) async {
        let totalInteractions = energyStatistics.interactionsCount
        
        let badgeType: EABadgeType?
        switch totalInteractions {
        case 50:
            badgeType = .socialNovice
        case 200:
            badgeType = .socialExpert
        case 500:
            badgeType = .socialMaster
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "社交达人")
        }
    }
    
    /// 检查等级成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - level: 等级
    private func checkLevelBadges(userId: UUID, level: Int) async {
        let badgeType: EABadgeType?
        
        switch level {
        case 3:
            badgeType = .noviceExplorer
        case 6:
            badgeType = .stellarTraveler
        case 10:
            badgeType = .cosmicNavigator
        default:
            badgeType = nil
        }
        
        if let badgeType = badgeType {
            await awardBadge(userId: userId, badgeType: badgeType, context: "等级成就")
        }
    }
    
    /// 颁发成就徽章
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - badgeType: 徽章类型
    ///   - context: 获得上下文
    private func awardBadge(userId: UUID, badgeType: EABadgeType, context: String) async {
        let badgeEarned = EABadgeEarned(
            userId: userId,
            badgeType: badgeType,
            title: badgeType.title,
            description: badgeType.description,
            earnedAt: Date(),
            context: context
        )
        
        recentBadgeEarned = badgeEarned
        energyStatistics.badgesEarned += 1
        
        // 徽章奖励能量
        let bonusEnergy = energyConfig.badgeBonus
        await updateUserStellarEnergy(userId: userId, energyGained: bonusEnergy)
    }
    
    // MARK: - 辅助计算方法
    
    /// 计算习惯难度倍数
    /// - Parameter habit: 习惯
    /// - Returns: 难度倍数
    private func calculateDifficultyMultiplier(for habit: EAHabit) -> Double {
        // 根据习惯频率计算难度
        switch habit.targetFrequency {
        case 1...3:
            return 1.0 // 简单习惯
        case 4...6:
            return 1.2 // 中等习惯
        case 7...:
            return 1.5 // 困难习惯
        default:
            return 1.0
        }
    }
    
    /// 计算连击奖励
    /// - Parameter streak: 连击天数
    /// - Returns: 奖励能量
    private func calculateStreakBonus(streak: Int) -> Int {
        switch streak {
        case 3...6:
            return 5
        case 7...13:
            return 10
        case 14...29:
            return 20
        case 30...:
            return 50
        default:
            return 0
        }
    }
    
    /// 计算等级奖励
    /// - Parameter userLevel: 用户等级
    /// - Returns: 等级奖励能量
    private func calculateLevelBonus(userLevel: Int) -> Int {
        return userLevel * 2 // 每级增加2点额外能量
    }
    
    /// 计算当前连击天数
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    /// - Returns: 连击天数
    private func calculateCurrentStreak(habitId: UUID, userId: UUID) async -> Int {
        let recentCompletions = await repositoryContainer.habitRepository.fetchRecentCompletions(userId: userId, days: 365)
        let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
        
        // 简化连击计算：连续完成的天数
        let calendar = Calendar.current
        let today = Date()
        var streak = 0
        
        for day in 0..<365 {
            let checkDate = calendar.date(byAdding: .day, value: -day, to: today)!
            let dayStart = calendar.startOfDay(for: checkDate)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
            
            let hasCompletion = habitCompletions.contains { completion in
                completion.date >= dayStart && completion.date < dayEnd
            }
            
            if hasCompletion {
                streak += 1
            } else {
                break
            }
        }
        
        return streak
    }
    
    /// 更新用户星际能量
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - energyGained: 获得的能量
    private func updateUserStellarEnergy(userId: UUID, energyGained: Int) async {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + energyGained
    }
    
    // MARK: - 公开接口
    
    /// 奖励星际能量给用户
    /// - Parameters:
    ///   - user: 目标用户
    ///   - amount: 奖励数量
    ///   - source: 能量来源
    ///   - description: 奖励描述
    func awardStellarEnergy(to user: EAUser, amount: Int, source: String, description: String) async {
        guard let socialProfile = user.socialProfile else { return }
        
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        socialProfile.totalStellarEnergy = currentEnergy + amount
        
        // 更新统计
        energyStatistics.totalEnergyEarned += amount
        
        // 检查等级升级
        await checkLevelUpgrade(userId: user.id)
        
        // 记录能量获得事件（可以在这里添加日志或通知）
        // 生产环境中应使用正式的日志系统或通知机制
    }
    
    /// 获取用户星际能量概览
    /// - Parameter userId: 用户ID
    /// - Returns: 能量概览
    func getUserEnergyOverview(userId: UUID) async -> EAUserEnergyOverview? {
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return nil }
        
        let currentLevel = socialProfile.stellarLevel ?? 1
        let totalEnergy = socialProfile.totalStellarEnergy ?? 0
        let currentTitle = getStellarTitle(level: currentLevel)
        
        // 计算到下一级的进度
        let nextLevelEnergy = getNextLevelRequirement(currentLevel: currentLevel)
        let currentLevelEnergy = getCurrentLevelRequirement(currentLevel: currentLevel)
        let progressToNext = Double(totalEnergy - currentLevelEnergy) / Double(nextLevelEnergy - currentLevelEnergy)
        
        return EAUserEnergyOverview(
            userId: userId,
            stellarLevel: currentLevel,
            explorerTitle: currentTitle,
            totalStellarEnergy: totalEnergy,
            progressToNextLevel: max(0, min(1, progressToNext)),
            nextLevelRequirement: nextLevelEnergy
        )
    }
    
    /// 获取当前等级要求
    /// - Parameter currentLevel: 当前等级
    /// - Returns: 当前等级所需能量
    private func getCurrentLevelRequirement(currentLevel: Int) -> Int {
        switch currentLevel {
        case 1...3:
            return (currentLevel - 1) * 333
        case 4...6:
            return 1000 + (currentLevel - 4) * 1333
        case 7...10:
            return 5000 + (currentLevel - 7) * 2500
        default:
            return 0
        }
    }
    
    /// 获取下一等级要求
    /// - Parameter currentLevel: 当前等级
    /// - Returns: 下一等级所需能量
    private func getNextLevelRequirement(currentLevel: Int) -> Int {
        let nextLevel = min(10, currentLevel + 1)
        return getCurrentLevelRequirement(currentLevel: nextLevel)
    }
    
    /// 重置能量统计
    func resetStatistics() {
        energyStatistics = EAEnergyStatistics()
    }
}

// MARK: - 数据模型

/// 星际能量配置
struct EAStellarEnergyConfig {
    let baseHabitCompletionEnergy = 10      // 基础习惯完成能量
    let milestoneShareEnergy = 50           // 里程碑分享能量
    let reflectionShareEnergy = 30          // 反思分享能量
    let achievementShareEnergy = 40         // 成就分享能量
    let motivationShareEnergy = 25          // 激励分享能量
    let likeEnergy = 2                      // 点赞能量
    let commentEnergy = 5                   // 评论能量
    let shareEnergy = 8                     // 转发能量
    let followEnergy = 10                   // 关注能量
    let levelUpBonus = 100                  // 等级升级奖励
    let badgeBonus = 30                     // 徽章奖励能量
}

/// 分享类型
enum EASharingType {
    case habitMilestone    // 习惯里程碑
    case reflection        // 反思分享
    case achievement       // 成就分享
    case motivation        // 激励分享
}

/// 互动类型
enum EAInteractionType {
    case like              // 点赞
    case comment           // 评论
    case share             // 转发
    case follow            // 关注
}

/// 徽章类型
enum EABadgeType: String, CaseIterable {
    case weekStreak = "week_streak"
    case monthStreak = "month_streak"
    case hundredDayStreak = "hundred_day_streak"
    case yearStreak = "year_streak"
    case sharingNovice = "sharing_novice"
    case sharingExpert = "sharing_expert"
    case sharingMaster = "sharing_master"
    case socialNovice = "social_novice"
    case socialExpert = "social_expert"
    case socialMaster = "social_master"
    case noviceExplorer = "novice_explorer"
    case stellarTraveler = "stellar_traveler"
    case cosmicNavigator = "cosmic_navigator"
    
    var title: String {
        switch self {
        case .weekStreak: return "七日连击"
        case .monthStreak: return "月度坚持"
        case .hundredDayStreak: return "百日成就"
        case .yearStreak: return "年度传奇"
        case .sharingNovice: return "分享新手"
        case .sharingExpert: return "分享专家"
        case .sharingMaster: return "分享大师"
        case .socialNovice: return "社交新星"
        case .socialExpert: return "社交达人"
        case .socialMaster: return "社交领袖"
        case .noviceExplorer: return "新手探索者"
        case .stellarTraveler: return "星际旅者"
        case .cosmicNavigator: return "宇宙领航员"
        }
    }
    
    var description: String {
        switch self {
        case .weekStreak: return "连续7天完成习惯"
        case .monthStreak: return "连续30天完成习惯"
        case .hundredDayStreak: return "连续100天完成习惯"
        case .yearStreak: return "连续365天完成习惯"
        case .sharingNovice: return "分享10次内容"
        case .sharingExpert: return "分享50次内容"
        case .sharingMaster: return "分享100次内容"
        case .socialNovice: return "社区互动50次"
        case .socialExpert: return "社区互动200次"
        case .socialMaster: return "社区互动500次"
        case .noviceExplorer: return "达到星际等级3级"
        case .stellarTraveler: return "达到星际等级6级"
        case .cosmicNavigator: return "达到星际等级10级"
        }
    }
}

/// 能量统计
struct EAEnergyStatistics {
    var totalEnergyEarned = 0      // 总获得能量
    var habitsCompleted = 0        // 完成习惯数
    var postsShared = 0           // 分享帖子数
    var interactionsCount = 0     // 互动次数
    var levelsGained = 0          // 升级次数
    var badgesEarned = 0          // 获得徽章数
}

/// 等级升级事件
struct EALevelUpEvent {
    let userId: UUID
    let oldLevel: Int
    let newLevel: Int
    let newTitle: String
    let timestamp: Date
}

/// 徽章获得事件
struct EABadgeEarned {
    let userId: UUID
    let badgeType: EABadgeType
    let title: String
    let description: String
    let earnedAt: Date
    let context: String
}

/// 用户能量概览
struct EAUserEnergyOverview {
    let userId: UUID
    let stellarLevel: Int
    let explorerTitle: String
    let totalStellarEnergy: Int
    let progressToNextLevel: Double
    let nextLevelRequirement: Int
} 