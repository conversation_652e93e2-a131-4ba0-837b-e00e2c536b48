import Foundation
import SwiftUI

// MARK: - 挑战状态枚举
enum EAChallengeState: String, CaseIterable {
    case upcoming = "upcoming"
    case active = "active"
    case ended = "ended"
    case expired = "expired"
    
    var displayName: String {
        switch self {
        case .upcoming:
            return "即将开始"
        case .active:
            return "进行中"
        case .ended:
            return "已结束"
        case .expired:
            return "已过期"
        }
    }
}

// MARK: - 宇宙挑战业务逻辑管理器（Phase 4 Day 9新增）

/// 宇宙挑战业务逻辑管理器：负责挑战生命周期、进度跟踪、奖励发放等复杂业务逻辑
@MainActor
class EAUniverseChallengeBusinessLogic: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var isProcessing = false
    @Published var challengeStats = EAChallengeStats.empty
    @Published var recentRewards: [EAChallengeReward] = []
    @Published var systemNotifications: [EASystemNotification] = []
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let stellarEnergyService: EAStellarEnergyService
    private let challengeService: EAUniverseChallengeService
    
    // MARK: - 配置
    
    private let lifecycleConfig = EAChallengeLifecycleConfig()
    private let progressConfig = EAChallengeProgressConfig()
    private let rewardConfig = EAChallengeRewardConfig()
    
    // MARK: - 初始化
    
    init(
        repositoryContainer: EARepositoryContainer,
        stellarEnergyService: EAStellarEnergyService,
        challengeService: EAUniverseChallengeService
    ) {
        self.repositoryContainer = repositoryContainer
        self.stellarEnergyService = stellarEnergyService
        self.challengeService = challengeService
    }
    
    // MARK: - 挑战生命周期管理
    
    /// 系统自动生成周期性挑战
    func generatePeriodicChallenges() async {
        guard !isProcessing else { return }
        
        isProcessing = true
        defer { isProcessing = false }
        
        do {
            // 检查当前活跃挑战数量
            let activeChallenges = try await repositoryContainer.challengeRepository.fetchActiveChallenges()
            
            if activeChallenges.count < lifecycleConfig.minActiveChallenges {
                // 生成新挑战
                let newChallenges = await createSystemChallenges()
                
                for challenge in newChallenges {
                    try await repositoryContainer.challengeRepository.createChallenge(challenge)
                }
                
                addSystemNotification("系统生成了 \(newChallenges.count) 个新挑战")
            }
            
            // 更新统计数据
            await updateChallengeStats()
            
        } catch {
            addSystemNotification("生成周期性挑战失败：\(error.localizedDescription)")
        }
    }
    
    /// 创建系统挑战
    private func createSystemChallenges() async -> [EAUniverseChallenge] {
        var challenges: [EAUniverseChallenge] = []
        
        // 获取热门习惯类别
        let popularCategories = await getPopularHabitCategories()
        
        for category in popularCategories.prefix(3) {
            let challenge = createChallengeForCategory(category)
            challenges.append(challenge)
        }
        
        return challenges
    }
    
    /// 获取热门习惯类别
    private func getPopularHabitCategories() async -> [String] {
        // 在实际项目中，这里会分析用户习惯数据
        return ["morning_routine", "fitness", "mindfulness", "reading", "productivity"]
    }
    
    /// 为特定类别创建挑战
    private func createChallengeForCategory(_ category: String) -> EAUniverseChallenge {
        let calendar = Calendar.current
        let now = Date()
        
        let challengeTemplates = getChallengeTemplates(for: category)
        let template = challengeTemplates.randomElement() ?? challengeTemplates[0]
        
        let challenge = EAUniverseChallenge(
            title: template.title,
            challengeDescription: template.description,
            targetHabitCategory: category,
            startDate: calendar.date(byAdding: .day, value: 1, to: now) ?? now,
            endDate: calendar.date(byAdding: .day, value: template.duration + 1, to: now) ?? now,
            stellarReward: template.baseReward
        )
        
        challenge.difficulty = template.difficulty
        challenge.challengeType = template.type
        challenge.targetValue = template.targetValue
        challenge.universeRegion = template.universeRegion
        challenge.challengeBadge = template.badge
        challenge.cosmicDifficulty = template.cosmicDifficulty
        challenge.energyMultiplier = template.energyMultiplier
        
        return challenge
    }
    
    /// 获取挑战模板
    private func getChallengeTemplates(for category: String) -> [EAChallengeTemplate] {
        switch category {
        case "morning_routine":
            return [
                EAChallengeTemplate(
                    title: "银河系早起探索者",
                    description: "连续7天早起完成晨间习惯，探索银河系的奥秘",
                    type: "habit_completion",
                    difficulty: "normal",
                    duration: 7,
                    targetValue: 7,
                    baseReward: 500,
                    universeRegion: "银河系",
                    badge: "explorer_badge",
                    cosmicDifficulty: 2,
                    energyMultiplier: 1.5
                ),
                EAChallengeTemplate(
                    title: "晨曦宇宙启航者",
                    description: "14天晨间习惯挑战，开启宇宙探索之旅",
                    type: "streak_goal",
                    difficulty: "hard",
                    duration: 14,
                    targetValue: 14,
                    baseReward: 800,
                    universeRegion: "仙女座",
                    badge: "pioneer_badge",
                    cosmicDifficulty: 3,
                    energyMultiplier: 2.0
                )
            ]
        case "fitness":
            return [
                EAChallengeTemplate(
                    title: "星际健身征程",
                    description: "本周完成5次运动习惯，增强星际探索体能",
                    type: "habit_completion",
                    difficulty: "easy",
                    duration: 7,
                    targetValue: 5,
                    baseReward: 300,
                    universeRegion: "猎户座",
                    badge: "warrior_badge",
                    cosmicDifficulty: 1,
                    energyMultiplier: 1.2
                ),
                EAChallengeTemplate(
                    title: "宇宙战士训练营",
                    description: "30天健身挑战，锻造宇宙战士体魄",
                    type: "streak_goal",
                    difficulty: "extreme",
                    duration: 30,
                    targetValue: 25,
                    baseReward: 1500,
                    universeRegion: "天鹅座",
                    badge: "champion_badge",
                    cosmicDifficulty: 5,
                    energyMultiplier: 3.0
                )
            ]
        case "mindfulness":
            return [
                EAChallengeTemplate(
                    title: "宇宙冥想大师",
                    description: "连续14天完成冥想习惯，达到宇宙意识层次",
                    type: "streak_goal",
                    difficulty: "hard",
                    duration: 14,
                    targetValue: 14,
                    baseReward: 800,
                    universeRegion: "仙女座",
                    badge: "master_badge",
                    cosmicDifficulty: 4,
                    energyMultiplier: 2.0
                )
            ]
        case "reading":
            return [
                EAChallengeTemplate(
                    title: "传奇阅读征服者",
                    description: "30天阅读挑战，征服知识宇宙的边界",
                    type: "habit_completion",
                    difficulty: "extreme",
                    duration: 30,
                    targetValue: 25,
                    baseReward: 1200,
                    universeRegion: "天鹅座",
                    badge: "legend_badge",
                    cosmicDifficulty: 5,
                    energyMultiplier: 3.0
                )
            ]
        default:
            return [
                EAChallengeTemplate(
                    title: "宇宙探索者",
                    description: "7天习惯挑战，开启宇宙探索之旅",
                    type: "habit_completion",
                    difficulty: "normal",
                    duration: 7,
                    targetValue: 5,
                    baseReward: 400,
                    universeRegion: "银河系",
                    badge: "explorer_badge",
                    cosmicDifficulty: 2,
                    energyMultiplier: 1.5
                )
            ]
        }
    }
    
    /// 管理挑战状态转换
    func manageChallengeStates() async {
        guard !isProcessing else { return }
        
        isProcessing = true
        defer { isProcessing = false }
        
        do {
            let allChallenges = try await repositoryContainer.challengeRepository.fetchActiveChallenges()
            let now = Date()
            
            for challenge in allChallenges {
                // 检查状态变化
                let newState = determineNewChallengeState(challenge)
                if newState.rawValue != challenge.currentState {
                    await updateChallengeState(challenge, newState: newState)
                }
            }
            
            // 归档过期挑战
            await archiveExpiredChallenges()
            
            // 更新统计数据
            await updateChallengeStats()
            
        } catch {
            addSystemNotification("管理挑战状态失败：\(error.localizedDescription)")
        }
    }
    
    /// 计算挑战状态
    private func calculateChallengeState(challenge: EAUniverseChallenge, currentTime: Date) -> EAChallengeState {
        if currentTime < challenge.startDate {
            return .upcoming
        } else if currentTime >= challenge.startDate && currentTime <= challenge.endDate {
            return .active
        } else {
            return .ended
        }
    }
    
    /// 更新挑战状态
    private func updateChallengeState(_ challenge: EAUniverseChallenge, newState: EAChallengeState) async {
        challenge.currentState = newState.rawValue
        challenge.status = newState.rawValue
        
        // 保存更改
        do {
            try await repositoryContainer.challengeRepository.saveContext()
        } catch {
            // 错误处理
        }
    }
    
    /// 确定挑战的新状态
    private func determineNewChallengeState(_ challenge: EAUniverseChallenge) -> EAChallengeState {
        let now = Date()
        
        if now < challenge.startDate {
            return .upcoming
        } else if now >= challenge.startDate && now <= challenge.endDate {
            return .active
        } else {
            return .ended
        }
    }
    
    /// 归档过期挑战
    private func archiveExpiredChallenges() async {
        do {
            let expiredChallenges = try await repositoryContainer.challengeRepository.fetchExpiredChallenges()
            
            for challenge in expiredChallenges {
                challenge.isActive = false
                challenge.isArchived = true
            }
            
            try await repositoryContainer.challengeRepository.saveContext()
            
            if !expiredChallenges.isEmpty {
                addSystemNotification("已归档 \(expiredChallenges.count) 个过期挑战")
            }
            
        } catch {
            addSystemNotification("归档过期挑战失败：\(error.localizedDescription)")
        }
    }
    
    // MARK: - 挑战进度跟踪算法
    
    /// 计算用户挑战进度
    func calculateUserProgress(challengeId: UUID, userId: UUID) async -> EAChallengeProgress {
        do {
            guard let challenge = try await repositoryContainer.challengeRepository.fetchChallenge(by: challengeId) else {
                return EAChallengeProgress.empty
            }
            
            guard let participation = try await repositoryContainer.challengeRepository.fetchParticipation(challengeId: challengeId, userId: userId) else {
                return EAChallengeProgress.empty
            }
            
            let progress = await calculateProgressByType(challenge: challenge, userId: userId)
            
            // 更新参与记录
            participation.currentProgress = progress.currentValue
            participation.progressPercentage = Double(progress.percentage) / 100.0
            participation.lastUpdateDate = Date()
            
            // 检查里程碑
            await checkProgressMilestones(challenge: challenge, participation: participation, progress: progress)
            
            try await repositoryContainer.challengeRepository.saveContext()
            
            return progress
            
        } catch {
            addSystemNotification("计算进度失败：\(error.localizedDescription)")
            return EAChallengeProgress.empty
        }
    }
    
    /// 根据挑战类型计算进度
    private func calculateProgressByType(challenge: EAUniverseChallenge, userId: UUID) async -> EAChallengeProgress {
        switch challenge.challengeType {
        case "habit_completion":
            return await calculateHabitCompletionProgress(challenge: challenge, userId: userId)
        case "streak_goal":
            return await calculateStreakProgress(challenge: challenge, userId: userId)
        case "community_goal":
            return await calculateCommunityProgress(challenge: challenge, userId: userId)
        default:
            return await calculateHabitCompletionProgress(challenge: challenge, userId: userId)
        }
    }
    
    /// 计算习惯完成进度
    private func calculateHabitCompletionProgress(challenge: EAUniverseChallenge, userId: UUID) async -> EAChallengeProgress {
        // 获取用户在挑战期间的习惯完成记录
        do {
            let completions = try await repositoryContainer.completionRepository.fetchCompletions(
                for: userId,
                date: challenge.startDate
            )
            
            // 计算完成数量
            let challengeCompletions = completions.filter { completion in
                completion.date >= challenge.startDate && completion.date <= challenge.endDate
            }
            
            let currentValue = challengeCompletions.count
            let percentage = min(100, (currentValue * 100) / challenge.targetValue)
            
            return EAChallengeProgress(
                challengeId: challenge.id,
                userId: userId,
                currentValue: currentValue,
                targetValue: challenge.targetValue,
                percentage: percentage,
                progressType: .habitCompletion,
                lastUpdateDate: Date()
            )
            
        } catch {
            return EAChallengeProgress(
                challengeId: challenge.id,
                userId: userId,
                currentValue: 0,
                targetValue: challenge.targetValue,
                percentage: 0,
                progressType: .habitCompletion,
                lastUpdateDate: Date()
            )
        }
    }
    
    /// 计算连续完成进度
    private func calculateStreakProgress(challenge: EAUniverseChallenge, userId: UUID) async -> EAChallengeProgress {
        // 获取用户连续完成记录
        do {
            let completions = try await repositoryContainer.completionRepository.fetchCompletions(
                for: userId,
                date: challenge.startDate
            )
            
            // 计算连续天数
            var consecutiveDays = 0
            let calendar = Calendar.current
            var currentDate = challenge.startDate
            
            while currentDate <= Date() && currentDate <= challenge.endDate {
                let dayCompletions = completions.filter { completion in
                    calendar.isDate(completion.date, inSameDayAs: currentDate)
                }
                
                if dayCompletions.isEmpty {
                    break
                } else {
                    consecutiveDays += 1
                }
                
                currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
            }
            
            let percentage = min(100, (consecutiveDays * 100) / challenge.targetValue)
            
            return EAChallengeProgress(
                challengeId: challenge.id,
                userId: userId,
                currentValue: consecutiveDays,
                targetValue: challenge.targetValue,
                percentage: percentage,
                progressType: .streakGoal,
                lastUpdateDate: Date()
            )
            
        } catch {
            return EAChallengeProgress(
                challengeId: challenge.id,
                userId: userId,
                currentValue: 0,
                targetValue: challenge.targetValue,
                percentage: 0,
                progressType: .streakGoal,
                lastUpdateDate: Date()
            )
        }
    }
    
    /// 计算社区进度
    private func calculateCommunityProgress(challenge: EAUniverseChallenge, userId: UUID) async -> EAChallengeProgress {
        // 计算社区整体进度
        do {
            let allParticipations = try await repositoryContainer.challengeRepository.fetchChallengeParticipations(challengeId: challenge.id)
            
            let totalProgress = allParticipations.reduce(0) { $0 + $1.currentProgress }
            let averageProgress = allParticipations.isEmpty ? 0 : totalProgress / allParticipations.count
            
            let percentage = min(100, (averageProgress * 100) / challenge.targetValue)
            
            return EAChallengeProgress(
                challengeId: challenge.id,
                userId: userId,
                currentValue: averageProgress,
                targetValue: challenge.targetValue,
                percentage: percentage,
                progressType: .communityGoal,
                lastUpdateDate: Date()
            )
            
        } catch {
            return EAChallengeProgress.empty
        }
    }
    
    /// 检查进度里程碑
    private func checkProgressMilestones(challenge: EAUniverseChallenge, participation: EAUniverseChallengeParticipation, progress: EAChallengeProgress) async {
        let milestones = [25, 50, 75, 100]
        
        for milestone in milestones {
            if progress.percentage >= milestone && !participation.achievedMilestones.contains(milestone) {
                participation.achievedMilestones.append(milestone)
                
                // 发放里程碑奖励
                let milestoneReward = calculateMilestoneReward(challenge: challenge, milestone: milestone)
                await awardMilestoneReward(userId: progress.userId, challenge: challenge, milestone: milestone, reward: milestoneReward)
                
                addSystemNotification("用户达成挑战里程碑：\(milestone)%")
            }
        }
    }
    
    // MARK: - 星际奖励发放系统
    
    /// 处理挑战完成
    private func processChallengeCompletion(challenge: EAUniverseChallenge) async {
        do {
            let participations = try await repositoryContainer.challengeRepository.fetchChallengeParticipations(challengeId: challenge.id)
            
            // 计算排名
            let rankedParticipations = participations.sorted { $0.progressPercentage > $1.progressPercentage }
            
            for (index, participation) in rankedParticipations.enumerated() {
                let ranking = index + 1
                await processParticipationCompletion(challenge: challenge, participation: participation, ranking: ranking, totalParticipants: participations.count)
            }
            
            addSystemNotification("挑战「\(challenge.title)」完成处理，共 \(participations.count) 人参与")
            
        } catch {
            addSystemNotification("处理挑战完成失败：\(error.localizedDescription)")
        }
    }
    
    /// 处理单个参与者完成
    private func processParticipationCompletion(challenge: EAUniverseChallenge, participation: EAUniverseChallengeParticipation, ranking: Int, totalParticipants: Int) async {
        guard let user = participation.user else { return }
        
        var totalReward = 0
        var rewardDetails: [String] = []
        
        // 基础完成奖励
        if participation.progressPercentage >= 100 {
            let baseReward = challenge.calculateBaseStellarReward()
            totalReward += baseReward
            rewardDetails.append("完成奖励: \(baseReward)")
            
            participation.isCompleted = true
            participation.completionDate = Date()
        }
        
        // 排名奖励（前10%）
        let topPercentThreshold = max(1, totalParticipants / 10)
        if ranking <= topPercentThreshold {
            let rankingReward = calculateRankingReward(challenge: challenge, ranking: ranking, totalParticipants: totalParticipants)
            totalReward += rankingReward
            rewardDetails.append("排名奖励: \(rankingReward)")
            
            participation.finalRanking = ranking
        }
        
        // 完美完成奖励（100%完成率）
        if participation.progressPercentage >= 100 {
            let perfectReward = calculatePerfectCompletionReward(challenge: challenge)
            totalReward += perfectReward
            rewardDetails.append("完美完成: \(perfectReward)")
            
            participation.isPerfectCompletion = true
        }
        
        // 防作弊检查
        if await isValidCompletion(participation: participation, challenge: challenge) {
            // 发放奖励
            participation.earnedReward = totalReward
            participation.stellarEnergyEarned = totalReward
            
            await stellarEnergyService.awardStellarEnergy(
                to: user,
                amount: totalReward,
                source: "challenge_completion",
                description: "完成挑战：\(challenge.title)"
            )
            
            // 记录奖励
            let reward = EAChallengeReward(
                userId: user.id,
                challengeId: challenge.id,
                amount: totalReward,
                rewardType: .completion,
                details: rewardDetails.joined(separator: ", "),
                awardedDate: Date()
            )
            
            recentRewards.append(reward)
            
            addSystemNotification("用户获得挑战奖励：\(totalReward) 星际能量")
        } else {
            addSystemNotification("检测到异常完成数据，奖励被拒绝")
        }
    }
    
    /// 计算排名奖励
    private func calculateRankingReward(challenge: EAUniverseChallenge, ranking: Int, totalParticipants: Int) -> Int {
        let baseReward = challenge.calculateBaseStellarReward()
        
        switch ranking {
        case 1:
            return Int(Double(baseReward) * rewardConfig.firstPlaceMultiplier)
        case 2:
            return Int(Double(baseReward) * rewardConfig.secondPlaceMultiplier)
        case 3:
            return Int(Double(baseReward) * rewardConfig.thirdPlaceMultiplier)
        default:
            let topPercentThreshold = max(1, totalParticipants / 10)
            if ranking <= topPercentThreshold {
                return Int(Double(baseReward) * rewardConfig.topPercentMultiplier)
            }
            return 0
        }
    }
    
    /// 计算完美完成奖励
    private func calculatePerfectCompletionReward(challenge: EAUniverseChallenge) -> Int {
        let baseReward = challenge.calculateBaseStellarReward()
        return Int(Double(baseReward) * rewardConfig.perfectCompletionMultiplier)
    }
    
    /// 计算里程碑奖励
    private func calculateMilestoneReward(challenge: EAUniverseChallenge, milestone: Int) -> Int {
        let baseReward = challenge.calculateBaseStellarReward()
        let multiplier = Double(milestone) / 100.0 * rewardConfig.milestoneMultiplier
        return Int(Double(baseReward) * multiplier)
    }
    
    /// 发放里程碑奖励
    private func awardMilestoneReward(userId: UUID, challenge: EAUniverseChallenge, milestone: Int, reward: Int) async {
        guard let user = await repositoryContainer.userRepository.fetchUser(by: userId) else { return }
        
        await stellarEnergyService.awardStellarEnergy(
            to: user,
            amount: reward,
            source: "challenge_milestone",
            description: "挑战里程碑：\(milestone)%"
        )
        
        let milestoneReward = EAChallengeReward(
            userId: userId,
            challengeId: challenge.id,
            amount: reward,
            rewardType: .milestone,
            details: "里程碑：\(milestone)%",
            awardedDate: Date()
        )
        
        recentRewards.append(milestoneReward)
    }
    
    /// 防作弊验证
    private func isValidCompletion(participation: EAUniverseChallengeParticipation, challenge: EAUniverseChallenge) async -> Bool {
        // 检查完成时间是否合理
        let timeDiff = participation.lastUpdateDate.timeIntervalSince(participation.joinDate)
        let challengeDuration = challenge.endDate.timeIntervalSince(challenge.startDate)
        
        if timeDiff < challengeDuration * 0.1 {
            // 完成时间过短，可能作弊
            return false
        }
        
        // 检查进度增长是否异常
        if participation.progressPercentage > 100 {
            return false
        }
        
        // 检查是否重复奖励
        if participation.earnedReward > 0 {
            return false
        }
        
        return true
    }
    
    // MARK: - 辅助方法
    
    /// 更新挑战统计数据
    private func updateChallengeStats() async {
        do {
            let allChallenges = try await repositoryContainer.challengeRepository.fetchActiveChallenges()
            let activeChallenges = allChallenges.filter { $0.isActive }
            
            var totalParticipants = 0
            var totalRewards = 0
            
            for challenge in allChallenges {
                let participations = try await repositoryContainer.challengeRepository.fetchChallengeParticipations(challengeId: challenge.id)
                totalParticipants += participations.count
                totalRewards += participations.reduce(0) { $0 + $1.earnedReward }
            }
            
            challengeStats = EAChallengeStats(
                totalChallenges: allChallenges.count,
                activeChallenges: activeChallenges.count,
                completedChallenges: allChallenges.count - activeChallenges.count,
                totalParticipants: totalParticipants,
                totalRewardsDistributed: totalRewards
            )
            
        } catch {
            addSystemNotification("更新统计数据失败：\(error.localizedDescription)")
        }
    }
    
    /// 添加系统通知
    private func addSystemNotification(_ message: String) {
        let notification = EASystemNotification(
            id: UUID(),
            message: message,
            timestamp: Date(),
            type: .system
        )
        
        systemNotifications.append(notification)
        
        // 保持通知数量在合理范围内
        if systemNotifications.count > 50 {
            systemNotifications.removeFirst(systemNotifications.count - 50)
        }
    }
}

// MARK: - 配置类

/// 挑战生命周期配置
struct EAChallengeLifecycleConfig {
    let minActiveChallenges = 3
    let maxActiveChallenges = 10
    let challengeGenerationInterval: TimeInterval = 24 * 60 * 60 // 24小时
    let archiveDelay: TimeInterval = 7 * 24 * 60 * 60 // 7天
}

/// 挑战进度配置
struct EAChallengeProgressConfig {
    let updateInterval: TimeInterval = 60 * 60 // 1小时
    let milestones = [25, 50, 75, 100]
    let progressValidationThreshold = 0.1 // 10%
}

/// 挑战奖励配置
struct EAChallengeRewardConfig {
    let firstPlaceMultiplier = 2.0
    let secondPlaceMultiplier = 1.5
    let thirdPlaceMultiplier = 1.2
    let topPercentMultiplier = 1.1
    let perfectCompletionMultiplier = 0.5
    let milestoneMultiplier = 0.1
}

// MARK: - 数据模型

/// 挑战模板
struct EAChallengeTemplate {
    let title: String
    let description: String
    let type: String
    let difficulty: String
    let duration: Int
    let targetValue: Int
    let baseReward: Int
    let universeRegion: String
    let badge: String
    let cosmicDifficulty: Int
    let energyMultiplier: Double
}

/// 挑战进度
struct EAChallengeProgress {
    let challengeId: UUID
    let userId: UUID
    let currentValue: Int
    let targetValue: Int
    let percentage: Int
    let progressType: EAProgressType
    let lastUpdateDate: Date
    
    static let empty = EAChallengeProgress(
        challengeId: UUID(),
        userId: UUID(),
        currentValue: 0,
        targetValue: 1,
        percentage: 0,
        progressType: .habitCompletion,
        lastUpdateDate: Date()
    )
}

/// 进度类型
enum EAProgressType: String, CaseIterable {
    case habitCompletion = "habit_completion"
    case streakGoal = "streak_goal"
    case communityGoal = "community_goal"
}

/// 挑战奖励
struct EAChallengeReward: Identifiable {
    let id = UUID()
    let userId: UUID
    let challengeId: UUID
    let amount: Int
    let rewardType: EARewardType
    let details: String
    let awardedDate: Date
}

/// 奖励类型
enum EARewardType: String, CaseIterable {
    case completion = "completion"
    case ranking = "ranking"
    case milestone = "milestone"
    case perfect = "perfect"
}

/// 系统通知
struct EASystemNotification: Identifiable {
    let id: UUID
    let message: String
    let timestamp: Date
    let type: EANotificationType
}

/// 通知类型
enum EANotificationType: String, CaseIterable {
    case system = "system"
    case challenge = "challenge"
    case reward = "reward"
}

/// 挑战统计数据
struct EAChallengeStats {
    let totalChallenges: Int
    let activeChallenges: Int
    let completedChallenges: Int
    let totalParticipants: Int
    let totalRewardsDistributed: Int
    
    static let empty = EAChallengeStats(
        totalChallenges: 0,
        activeChallenges: 0,
        completedChallenges: 0,
        totalParticipants: 0,
        totalRewardsDistributed: 0
    )
} 