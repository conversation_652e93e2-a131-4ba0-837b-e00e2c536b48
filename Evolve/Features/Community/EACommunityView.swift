import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EACommunitySheetType: Identifiable {
    case createPost
    case postDetail(EACommunityPost)
    case imagePicker
    case challengeList  // ✅ 新增：挑战列表入口
    
    var id: String {
        switch self {
        case .createPost: return "createPost"
        case .postDetail(let post): return "postDetail_\(post.id)"
        case .imagePicker: return "imagePicker"
        case .challengeList: return "challengeList"  // ✅ 新增
        }
    }
}

/// 社区主页面 - 展示用户分享的习惯成果和互动
/// 遵循项目MVVM架构规范，使用Repository模式进行数据访问
/// ✅ 修复：实现统一Sheet状态管理，符合.cursorrules规范
struct EACommunityView: View {
    
    // MARK: - Properties
    
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @StateObject private var viewModel: EACommunityViewModel
    
    // MARK: - 状态管理
    
    /// 新帖子内容
    @State private var newPostContent = ""
    
    /// ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EACommunitySheetType?
    
    /// 搜索文本
    @State private var searchText: String = ""
    
    /// 选中的图片路径
    @State private var imagePaths: [String] = []
    

    
    // MARK: - Initialization
    
    init() {
        _viewModel = StateObject(wrappedValue: EACommunityViewModel())
    }
    
    // MARK: - 主视图
    
    var body: some View {
        NavigationView {
            ZStack {
                // 数字宇宙背景
                digitalUniverseBackground
                
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                
                // 内容区域
                contentArea
            }
            }
            .navigationTitle("星际信标网络")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                // ✅ 新增：挑战功能入口
                ToolbarItem(placement: .navigationBarLeading) {
                    challengeButton
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    createPostButton
                }
            }
            .onAppear {
                // 注入Repository容器
                if let container = repositoryContainer {
                    viewModel.setRepositoryContainer(container)
                }
                
                Task {
                    await viewModel.loadPosts()
                }
            }
            .refreshable {
                await viewModel.refreshPosts()
            }
            .sheet(item: $activeSheet) { sheet in
                switch sheet {
                case .createPost:
                    createPostSheet
                case .postDetail(let post):
                    NavigationView {
                        EAPostDetailView(post: post)
                            .environmentObject(sessionManager)
                    }
                case .imagePicker:
                    // 实现图片选择器
                    Text("Image Picker")
                case .challengeList:
                    // ✅ 新增：集成挑战列表页面
                    NavigationView {
                        EAUniverseChallengeListView()
                            .environmentObject(sessionManager)
                    }
                }
            }
        }
    }
    
    // MARK: - 子视图组件
    
    /// 星际信标搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.cyan)
            
            TextField("搜索星际信标...", text: $searchText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .onSubmit {
                    Task {
                        await viewModel.searchPosts(with: searchText)
                    }
                }
        }
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    /// 内容区域
    private var contentArea: some View {
        Group {
            if viewModel.isLoading && viewModel.posts.isEmpty {
                loadingView
            } else if viewModel.posts.isEmpty {
                emptyStateView
            } else {
                postsList
            }
        }
    }
    
    /// 加载视图 - 数字宇宙主题
    private var loadingView: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .cyan))
                .scaleEffect(1.5)
            Text("正在连接星际网络...")
                .foregroundColor(.cyan.opacity(0.8))
                .padding(.top, 12)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 空状态视图 - 数字宇宙主题
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            // 星际探索图标
            ZStack {
                Circle()
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [.cyan, .blue, .purple]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: 3
                    )
                    .frame(width: 100, height: 100)
                
                Image(systemName: "antenna.radiowaves.left.and.right")
                    .font(.system(size: 40))
                    .foregroundColor(.cyan)
            }
            
            Text("星际网络正在等待")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.white)
            
            Text("成为第一个广播探索成果的星际探索者！")
                .foregroundColor(.cyan.opacity(0.8))
                .multilineTextAlignment(.center)
            
            Button("初始化星际网络") {
                Task {
                    await viewModel.resetCommunityData()
                }
            }
            .buttonStyle(.borderedProminent)
            .tint(.cyan)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    /// 帖子列表
    private var postsList: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                ForEach(viewModel.posts, id: \.id) { post in
                    postCardView(post: post)
                }
                
                // 加载更多指示器
                if viewModel.isLoading && !viewModel.posts.isEmpty {
                    ProgressView()
                        .padding()
                }
            }
            .padding()
        }
    }
    
    /// 帖子卡片视图 - 使用数字宇宙主题组件
    private func postCardView(post: EACommunityPost) -> some View {
        // 🔑 修复：使用计算属性绑定，避免状态不同步
        let isLikedBinding = Binding<Bool>(
            get: {
                // TODO: 从用户数据中获取真实的点赞状态
                false
            },
            set: { _ in }
        )

        return EACommunityPostCard(
            post: post,
            isLiked: isLikedBinding,
            likeCount: post.likeCount,
            style: .standard,
            onLike: {
                Task {
                    // 🔑 修复：只调用ViewModel的toggleLike，让Repository层处理所有状态更新
                    await viewModel.toggleLike(for: post)
                    // 移除手动的isLiked.toggle()，避免双重更新
                }
            },
            onPostTap: {
                activeSheet = .postDetail(post)
            },
            onUserTap: {
                // TODO: 导航到用户页面
            },
            onImageTap: { imagePath, index in
                // TODO: 实现图片查看功能
            }
        )
        .onAppear {
            handlePostAppear(post: post)
        }
    }
    
    /// 处理帖子出现事件
    private func handlePostAppear(post: EACommunityPost) {
        // 分页加载
        if post.id == viewModel.posts.last?.id && viewModel.hasNextPage {
            Task {
                await viewModel.loadPosts()
            }
        }
    }
    
    /// 帖子上下文菜单
    private func postContextMenu(for post: EACommunityPost) -> some View {
        Group {
            Button("分享") {
                // TODO: 实现分享功能
            }
            
            // 只有帖子作者才能删除
            Button("删除", role: .destructive) {
                Task {
                    await viewModel.deletePost(post)
                }
            }
        }
    }
    
    /// 创建帖子按钮
    private var createPostButton: some View {
        Button {
            activeSheet = .createPost
        } label: {
            Image(systemName: "plus")
                .font(.title2)
        }
    }
    
    /// ✅ 新增：宇宙挑战功能入口按钮
    private var challengeButton: some View {
        Button {
            activeSheet = .challengeList
        } label: {
            Image(systemName: "star.circle.fill")
                .font(.title2)
                .foregroundColor(.yellow)
        }
    }
    
    /// 星际信标创建界面 - 数字宇宙主题
    private var createPostSheet: some View {
        NavigationView {
            ZStack {
                // 数字宇宙背景
                digitalUniverseBackground
                
                VStack(alignment: .leading, spacing: 20) {
                    // 星际信标标题
                    HStack {
                        Image(systemName: "antenna.radiowaves.left.and.right")
                            .font(.title2)
                            .foregroundColor(.cyan)
                        Text("广播你的探索成果")
                    .font(.title2)
                    .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                
                    // 信标内容输入框
                    VStack(alignment: .leading, spacing: 8) {
                        Text("信标内容")
                            .font(.headline)
                            .foregroundColor(.cyan)
                        
                TextEditor(text: $newPostContent)
                    .frame(minHeight: 120)
                            .padding(12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.black.opacity(0.6))
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [.cyan, .blue, .purple]),
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                ),
                                                lineWidth: 1
                                            )
                                    )
                            )
                            .foregroundColor(.white)
                            .scrollContentBackground(.hidden)
                    }
                
                Spacer()
            }
            .padding()
            }
            .navigationTitle("星际信标广播")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        activeSheet = nil
                        newPostContent = ""
                        imagePaths = []
                    }
                    .foregroundColor(.cyan)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("广播") {
                        Task {
                            await viewModel.createPost(
                                title: "星际探索", // 默认标题
                                content: newPostContent
                            )
                            activeSheet = nil
                            newPostContent = ""
                            imagePaths = []
                        }
                    }
                    .disabled(newPostContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    .foregroundColor(newPostContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? .gray : .cyan)
                    .fontWeight(.semibold)
                }
            }
            .toolbarBackground(.black.opacity(0.8), for: .navigationBar)
        }
    }
    
    /// 数字宇宙背景
    private var digitalUniverseBackground: some View {
        ZStack {
            // 深空渐变背景
            LinearGradient(
                gradient: Gradient(colors: [
                    Color.black,
                    Color.blue.opacity(0.3),
                    Color.purple.opacity(0.2),
                    Color.black
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // 星际网格效果
            GeometryReader { geometry in
                Path { path in
                    let cellSize: CGFloat = 30
                    let rows = Int(geometry.size.height / cellSize) + 1
                    let cols = Int(geometry.size.width / cellSize) + 1
                    
                    for row in 0...rows {
                        let y = CGFloat(row) * cellSize
                        path.move(to: CGPoint(x: 0, y: y))
                        path.addLine(to: CGPoint(x: geometry.size.width, y: y))
                    }
                    
                    for col in 0...cols {
                        let x = CGFloat(col) * cellSize
                        path.move(to: CGPoint(x: x, y: 0))
                        path.addLine(to: CGPoint(x: x, y: geometry.size.height))
                    }
                }
                .stroke(Color.cyan.opacity(0.1), lineWidth: 0.5)
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - 预览

#Preview("星际信标网络") {
    @MainActor
    func createPreview() -> some View {
        // ✅ 修复：使用本地实例替代单例
        let sessionManager = EASessionManager()
        
        return NavigationView {
            EACommunityView()
                .environmentObject(sessionManager)
                .task {
                    #if DEBUG
                    await sessionManager.simulateLogin()
                    #endif
                }
        }
        .modelContainer(PreviewData.container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
} 