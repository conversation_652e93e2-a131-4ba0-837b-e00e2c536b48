import SwiftUI
import SwiftData

/// 帖子详情页面
/// 显示帖子完整内容、评论列表和评论输入功能
/// 遵循MVVM架构，支持评论CRUD和点赞交互
struct EAPostDetailView: View {
    
    // MARK: - Properties
    
    /// 帖子数据
    let post: EACommunityPost
    
    /// 视图模型
    @StateObject private var viewModel: EAPostDetailViewModel
    
    /// Repository容器
    @Environment(\.repositoryContainer) private var repositoryContainer
    
    /// Session管理器
    @EnvironmentObject var sessionManager: EASessionManager
    
    /// 关闭页面
    @Environment(\.dismiss) private var dismiss
    
    /// 键盘监控
    @FocusState private var isCommentFieldFocused: Bool
    
    /// 图片查看器状态
    @State private var showImageViewer = false
    @State private var imageViewerPaths: [String] = []
    @State private var imageViewerInitialIndex = 0
    
    // MARK: - Initialization
    
    init(post: EACommunityPost) {
        self.post = post
        // ✅ 修复：使用临时实例初始化，在onAppear中设置正确的sessionManager
        _viewModel = StateObject(wrappedValue: EAPostDetailViewModel(post: post, sessionManager: EASessionManager()))
    }
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ZStack {
                // 生态主题背景
                Color.hexColor("002b20")
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 主要内容区域
                    ScrollView {
                        LazyVStack(spacing: 16, pinnedViews: [.sectionHeaders]) {
                            // 帖子内容区域
                            postContentSection
                            
                            // 评论区域
                            commentsSection
                        }
                        .padding(.horizontal, 16)
                    }
                    
                    // 评论输入区域
                    commentInputSection
                }
            }
            .navigationTitle("帖子详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 16, weight: .semibold))
                            Text("返回")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .foregroundColor(Color.hexColor("40E0D0"))
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    // 帖子操作菜单
                    postActionMenu
                }
            }
        }
        .alert("操作提示", isPresented: $viewModel.showAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            if let message = viewModel.alertMessage {
                Text(message)
            }
        }
        .fullScreenCover(isPresented: $showImageViewer) {
            EAImageViewer(
                imagePaths: imageViewerPaths,
                initialIndex: imageViewerInitialIndex,
                onDismiss: {
                    showImageViewer = false
                }
            )
        }
        .onAppear {
            // ✅ 修复：设置SessionManager和Repository容器
            viewModel.setSessionManager(sessionManager)
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
            viewModel.loadComments()
        }
    }
    
    // MARK: - Post Content Section
    
    private var postContentSection: some View {
        VStack(spacing: 0) {
            // 帖子卡片 - 复用EACommunityPostCard，禁用动画效果
            EACommunityPostCard(
                post: post,
                isLiked: Binding(
                    get: { viewModel.isPostLiked },
                    set: { _ in }
                ),
                likeCount: viewModel.getPostLikesCount(),
                style: .detail, // 使用详情页样式，禁用动画
                onLike: {
                    viewModel.togglePostLike()
                },
                onPostTap: {
                    // 已在详情页，无需响应点击
                },
                onUserTap: {
                    viewModel.showUserProfile()
                },
                onImageTap: { imagePath, index in
                    // 打开图片查看器
                    imageViewerPaths = post.imageURLs
                    imageViewerInitialIndex = index
                    showImageViewer = true
                }
            )
            .disabled(false) // 允许图片点击

            Divider()
                .background(Color.white.opacity(0.1))
                .padding(.top, 16)
        }
    }
    
    // MARK: - Comments Section
    
    private var commentsSection: some View {
        VStack(spacing: 0) {
            // 评论区标题
            commentsHeader
            
            // 评论内容 - 修复黑屏问题，添加错误恢复
            if viewModel.isLoadingComments {
                // 加载状态
                loadingCommentsView
                
            } else if let loadingError = viewModel.loadingError, viewModel.canRetry {
                // 错误状态 - 新增错误恢复UI
                errorStateView(loadingError)
                
            } else if viewModel.comments.isEmpty {
                // 空状态
                emptyCommentsView
                
            } else {
                // 评论列表
                commentsListView
            }
        }
    }
    
    // MARK: - Loading Comments View
    
    private var loadingCommentsView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
            
            Text("加载评论中...")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(height: 80)
    }
    
    // MARK: - Error State View
    
    private func errorStateView(_ errorMessage: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 32))
                .foregroundColor(.orange)
            
            VStack(spacing: 8) {
                Text("加载失败")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                Text(errorMessage)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
            }
            
            Button(action: {
                viewModel.retryLoadComments()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 14))
                    Text("重试")
                        .font(.system(size: 14, weight: .medium))
                }
                .foregroundColor(Color.hexColor("40E0D0"))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.hexColor("40E0D0"), lineWidth: 1)
                )
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
    
    // MARK: - Empty Comments View
    
    private var emptyCommentsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "bubble.left.and.bubble.right")
                .font(.system(size: 32))
                .foregroundColor(.white.opacity(0.3))
            
            Text("还没有评论")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white.opacity(0.6))
            
            Text("成为第一个评论的人吧！")
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white.opacity(0.5))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
    
    // MARK: - Comments List View
    
    private var commentsListView: some View {
        LazyVStack(spacing: 12) {
            ForEach(viewModel.comments) { comment in
                EACommentCell(
                    comment: comment,
                    isLiked: Binding(
                        get: { viewModel.isCommentLiked(comment) },
                        set: { _ in }
                    ),
                    likeCount: viewModel.getCommentLikesCount(comment),
                    style: comment.isReply ? .reply : .standard,
                    onLike: {
                        viewModel.toggleCommentLike(comment)
                    },
                    onReply: {
                        viewModel.prepareReplyToComment(comment)
                        isCommentFieldFocused = true
                    },
                    onUserTap: {
                        viewModel.showUserProfile(for: comment.author)
                    }
                )
            }
        }
    }

    // MARK: - Comments Header
    
    private var commentsHeader: some View {
        HStack {
            Text("评论 (\(viewModel.comments.count))")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
            
            Spacer()
            
            // 评论排序按钮
            Button(action: {
                viewModel.toggleCommentSort()
            }) {
                HStack(spacing: 4) {
                    Text(viewModel.commentSortText)
                        .font(.system(size: 14, weight: .medium))
                    
                    Image(systemName: "arrow.up.arrow.down")
                        .font(.system(size: 12))
                }
                .foregroundColor(Color.hexColor("40E0D0"))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.hexColor("002b20"))
    }
    
    // MARK: - Comment Input Section
    
    private var commentInputSection: some View {
        VStack(spacing: 0) {
            // 分隔线
            Divider()
                .background(Color.white.opacity(0.1))
            
            VStack(spacing: 8) {
                // 回复提示（如果是回复评论）
                if let replyTarget = viewModel.replyTargetComment {
                    replyIndicator(for: replyTarget)
                }
                
                // 评论输入框
                HStack(spacing: 12) {
                    // 用户头像
                    EAAvatarView(
                        avatarData: nil, // 当前用户头像
                        size: 32,
                        showShadow: false
                    )
                    
                    // 输入框
                    TextField("写下你的想法...", text: $viewModel.commentText, axis: .vertical)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color.white.opacity(0.1))
                        )
                        .focused($isCommentFieldFocused)
                        .lineLimit(1...4)
                    
                    // 发送按钮
                    Button(action: {
                        Task {
                            await viewModel.submitComment()
                        }
                        isCommentFieldFocused = false
                    }) {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(
                                viewModel.canSubmitComment ? Color.hexColor("40E0D0") : Color.white.opacity(0.3)
                            )
                    }
                    .disabled(!viewModel.canSubmitComment || viewModel.isSubmittingComment)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color.hexColor("002b20"))
        }
    }
    
    // MARK: - Reply Indicator
    
    private func replyIndicator(for comment: EACommunityComment) -> some View {
        HStack(spacing: 8) {
            Image(systemName: "arrowshape.turn.up.left.fill")
                .font(.system(size: 12))
                .foregroundColor(Color.hexColor("40E0D0"))
            
            Text("回复 @\(comment.getAuthorUsername())")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.hexColor("40E0D0"))
            
            Spacer()
            
            Button(action: {
                viewModel.cancelReply()
            }) {
                Image(systemName: "xmark")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.hexColor("40E0D0").opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .padding(.horizontal, 16)
    }
    
    // MARK: - Post Action Menu
    
    private var postActionMenu: some View {
        Menu {
            // 分享
            Button(action: {
                viewModel.sharePost()
            }) {
                Label("分享", systemImage: "square.and.arrow.up")
            }
            
            // 举报
            Button(action: {
                viewModel.reportPost()
            }) {
                Label("举报", systemImage: "flag")
            }
            
            // 删除（如果是作者）
            if viewModel.canDeletePost {
                Button(role: .destructive, action: {
                    viewModel.showDeleteConfirmation()
                }) {
                    Label("删除", systemImage: "trash")
                }
            }
        } label: {
            Image(systemName: "ellipsis")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
        }
        .confirmationDialog("删除帖子", isPresented: $viewModel.showDeleteDialog) {
            Button("删除", role: .destructive) {
                viewModel.deletePost()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("确定要删除这条帖子吗？删除后无法恢复。")
        }
    }
}

// MARK: - Preview

#Preview {
    // 使用社区预览数据的第一条帖子
    let samplePost = PreviewData.sampleCommunityPosts.first!
    let container = PreviewData.createCommunityPreviewContainer()
    
    EAPostDetailView(post: samplePost)
        .modelContainer(container)
        .preferredColorScheme(.dark)
} 