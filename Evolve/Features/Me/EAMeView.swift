import SwiftUI
import SwiftData

/// Sheet类型枚举 - 统一管理所有Sheet状态
enum EAMeSheetType: Identifiable {
    case achievementHall
    case proCenter
    case settings
    case about
    case avatarPicker
    case cosmicProfile
    case dataPersistenceTest  // 阶段4：数据持久化测试工具
    
    var id: String {
        switch self {
        case .achievementHall: return "achievementHall"
        case .proCenter: return "proCenter"
        case .settings: return "settings"
        case .about: return "about"
        case .avatarPicker: return "avatarPicker"
        case .cosmicProfile: return "cosmicProfile"
        case .dataPersistenceTest: return "dataPersistenceTest"
        }
    }
}

/// Alert类型枚举 - 统一管理所有Alert状态
enum EAMeAlertType: Identifiable {
    case logout
    case dataExport
    
    var id: String {
        switch self {
        case .logout: return "logout"
        case .dataExport: return "dataExport"
        }
    }
}

/// "我的"页面主视图
/// 包含用户信息展示、成就概览、Pro会员状态和功能导航
/// ✅ 修复：实现统一Sheet状态管理，符合.cursorrules规范
struct EAMeView: View {
    @EnvironmentObject var sessionManager: EASessionManager
    @Environment(\.repositoryContainer) private var repositoryContainer
    @StateObject private var viewModel: EAMeViewModel
    
    // ✅ 修复：统一Sheet状态管理
    @State private var activeSheet: EAMeSheetType?
    
    // ✅ 修复：统一Alert状态管理
    @State private var activeAlert: EAMeAlertType?
    
    // 编辑用户名状态（自定义overlay）
    @State private var showEditNameAlert = false
    
    // 其他状态
    @State private var editingName = ""
    @State private var errorMessage = ""
    @State private var isExporting = false
    @State private var exportMessage = ""
    @State private var profileMode: ProfileDisplayMode = .classic
    
    // 🔑 计算属性：直接绑定SessionManager的用户头像数据，确保实时同步
    private var userAvatarData: EAAvatarData? {
        return sessionManager.currentUser?.avatarData
    }
    
    init(viewModel: EAMeViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景延伸到整个视口（包括安全区域）
                EABackgroundView()
                    .ignoresSafeArea(.all)
                
                // 主内容区域 - 精确控制，确保内容永远不会进入安全区域
                VStack(spacing: 0) {
                    // 顶部安全区域占位 - 完全透明但占据空间
                    Rectangle()
                        .fill(Color.clear)
                        .frame(height: calculateSafeTopMaskHeight(geometry: geometry))
                        .allowsHitTesting(false) // 不接收触摸事件
                    
                    // 可滚动内容区域 - 严格限制在安全区域下方
                    ScrollView {
                        VStack(spacing: 24) {
                            // Phase 3 Day 7: 档案模式选择器
                            profileModeSelector
                                .padding(.top, 16)
                            
                            // 用户头像和信息
                            userProfileSection
                            
                            // Pro会员状态卡片
                            if sessionManager.currentUser?.isPro == true {
                                proMembershipCard
                            } else {
                                upgradeToProCard
                            }
                            
                            // 成就概览
                            achievementOverviewSection
                            
                            // 功能列表
                            functionsSection
                            
                            // 退出登录按钮
                            logoutSection
                            
                            // 底部间距 - 为Tab Bar留出空间
                            Spacer(minLength: 120)
                        }
                        .padding(.horizontal, 16)
                    }
                    .frame(
                        maxWidth: .infinity,
                        maxHeight: calculateContentHeight(geometry: geometry)
                    )
                    .contentMargins(.top, 0, for: .scrollContent) // iOS 17+ 特性：确保内容不会超出顶部
                    .clipped() // 严格裁剪，确保内容不会超出边界
                }
            }
        }
        // 🔑 关键修改：移除NavigationStack，改用sheet导航
        .sheet(item: $activeSheet) { sheet in
            switch sheet {
            case .achievementHall:
                EAAchievementHallView(viewModel: viewModel)
            case .proCenter:
                EAProMembershipView()
            case .settings:
                EASettingsView()
            case .about:
                EASettingsView()
            case .avatarPicker:
                EAAvatarPicker(avatarData: userAvatarData) { newAvatarData in
                    // 🔑 关键修复：头像数据变化时通过SessionManager保存
                    Task {
                        await saveAvatarDataThroughSessionManager(newAvatarData)
                    }
                }
            case .cosmicProfile:
                if let userId = sessionManager.currentUser?.id,
                   let repositoryContainer = viewModel.repositoryContainerReference {
                    // ✅ 修复：确保星际档案使用共享Repository容器，避免Context冲突
                    EACosmicExplorerProfileView(
                        userId: userId,
                        repositoryContainer: repositoryContainer,  // 使用共享的Repository容器
                        viewModel: viewModel
                    )
                    .environmentObject(sessionManager)
                } else {
                    // 降级处理：Repository容器未就绪时显示加载状态
                    VStack {
                        ProgressView("正在加载星际档案...")
                        Text("请稍候")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            case .dataPersistenceTest:
                EADataPersistenceTestView()
                    .environmentObject(sessionManager)
            }
        }
        .alert("确认退出", isPresented: Binding<Bool>(
            get: { activeAlert == .logout },
            set: { if !$0 { activeAlert = nil } }
        )) {
            Button("退出登录", role: .destructive) {
                handleLogout()
            }
            Button("取消", role: .cancel) {
                // 用户取消退出登录
            }
        } message: {
            Text("退出登录后需要重新登录才能使用应用")
        }
        .alert("数据导出", isPresented: Binding<Bool>(
            get: { activeAlert == .dataExport },
            set: { if !$0 { activeAlert = nil } }
        )) {
            Button("导出") {
                handleDataExport()
            }
            Button("取消", role: .cancel) { }
        } message: {
                            Text("将导出您的所有计划数据和统计信息")
        }
        .overlay(
            // 自定义编辑用户名弹窗
            editNameOverlay
        )
        .onAppear {
            // ✅ 修复：设置Repository容器，确保星际档案正常加载
            if let container = repositoryContainer {
                viewModel.setRepositoryContainer(container)
            }
            
            // 加载用户头像数据
            loadUserAvatarData()
            Task {
                await viewModel.loadUserStats()
                await viewModel.loadAchievements()
            }
        }
    }
    
    // MARK: - User Profile Section
    private var userProfileSection: some View {
        HStack(spacing: 16) {
            userAvatarView
            userInfoView
            Spacer()
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(userProfileBackground)
    }
    
    // MARK: - User Avatar View
    private var userAvatarView: some View {
        Button(action: {
            activeSheet = .avatarPicker
        }) {
            ZStack {
                EAAvatarView(avatarData: userAvatarData, size: 70)
                
                // 编辑指示器
                Circle()
                    .fill(Color.accentColor)
                    .frame(width: 24, height: 24)
                    .overlay(
                        Image(systemName: "pencil")
                            .font(.system(size: 12, weight: .semibold))
                            .foregroundColor(.white)
                    )
                    .offset(x: 25, y: 25)
                    .shadow(
                        color: Color.black.opacity(0.3),
                        radius: 4,
                        x: 0,
                        y: 2
                    )
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - User Info View
    private var userInfoView: some View {
        VStack(alignment: .leading, spacing: 8) {
            userNameText
            userEmailText
            membershipStatusBadge
        }
    }
    
    private var userNameText: some View {
        Button(action: {
            editingName = sessionManager.currentUser?.username ?? "测试用户"
            showEditNameAlert = true
        }) {
            HStack(spacing: 8) {
                Text(sessionManager.currentUser?.username ?? "测试用户")
                    .font(.system(size: 22, weight: .semibold))
                    .foregroundColor(.white)
                    .lineLimit(1)
                
                Image(systemName: "pencil")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.6))
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    @ViewBuilder
    private var userEmailText: some View {
                if let email = sessionManager.currentUser?.email {
                    Text(email)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(Color.white.opacity(0.7))
                .lineLimit(1)
        }
    }
    
    private var membershipStatusBadge: some View {
        HStack(spacing: 6) {
            membershipIcon
            membershipText
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 4)
        .background(membershipBadgeBackground)
    }
    
    private var membershipIcon: some View {
        Image(systemName: isProMember ? "crown.fill" : "person.circle")
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(isProMember ? Color.yellow : Color.white.opacity(0.6))
    }
    
    private var membershipText: some View {
        Text(isProMember ? "Pro会员" : "普通用户")
            .font(.system(size: 11, weight: .medium))
            .foregroundColor(isProMember ? Color.yellow : Color.white.opacity(0.6))
                }
    
    private var membershipBadgeBackground: some View {
        Capsule()
            .fill(Color.white.opacity(0.08))
                        .overlay(
                Capsule()
                                .stroke(
                        isProMember ? 
                        Color.yellow.opacity(0.4) : 
                        Color.white.opacity(0.2), 
                        lineWidth: 0.8
                    )
            )
    }
    
    private var userProfileBackground: some View {
        RoundedRectangle(cornerRadius: 16)
            .fill(Color.white.opacity(0.03))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.08), lineWidth: 1)
                )
            }
    
    private var isProMember: Bool {
        sessionManager.currentUser?.isPro == true
    }
    
    // MARK: - Pro Membership Card
    private var proMembershipCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(spacing: 8) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                        
                        Text("Pro会员")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    
                    if let expirationDate = sessionManager.currentUser?.proExpirationDate {
                        Text("有效期至 \(formatDateFromDate(expirationDate))")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                    } else {
                        Text("永久会员")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color.hexColor("FFD700"))
                    }
                }
                
                Spacer()
                
                Button(action: {
                    activeSheet = .proCenter
                }) {
                    Text("管理")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.hexColor("FFD700"))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.hexColor("FFD700").opacity(0.2))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.hexColor("FFD700").opacity(0.5), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.hexColor("FFD700").opacity(0.1),
                            Color.hexColor("FFA500").opacity(0.05)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.hexColor("FFD700").opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Upgrade to Pro Card
    private var upgradeToProCard: some View {
        Button(action: {
            activeSheet = .proCenter
        }) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack(spacing: 8) {
                            Image(systemName: "crown")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.hexColor("FFD700"))
                            
                            Text("升级Pro会员")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)
                        }
                        
                        Text("解锁更多AI功能和专属内容")
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(Color.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.hexColor("FFD700"))
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.hexColor("FFD700").opacity(0.1),
                                Color.hexColor("FFA500").opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.hexColor("FFD700").opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Achievement Overview Section
    private var achievementOverviewSection: some View {
        VStack(spacing: 16) {
            // 标题和查看全部按钮
            HStack {
                Text("我的成就")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    activeSheet = .achievementHall
                }) {
                    HStack(spacing: 4) {
                        Text("查看全部")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.hexColor("40E0D0"))
                        
                        Image(systemName: "chevron.right")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color.hexColor("40E0D0"))
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // 成就统计卡片
            HStack(spacing: 12) {
                // 已获得徽章
                AchievementStatCard(
                    title: "已获得",
                    value: "\(viewModel.achievements.filter { $0.isUnlocked }.count)",
                    subtitle: "个徽章",
                    color: Color.hexColor("40E0D0")
                )
                
                // 当前连击
                AchievementStatCard(
                    title: "当前连击",
                    value: "\(viewModel.userStats.currentStreak)",
                    subtitle: "天",
                    color: Color.hexColor("FF7F50")
                )
                
                // 完成率
                AchievementStatCard(
                    title: "完成率",
                    value: String(format: "%.0f%%", viewModel.userStats.completionRate * 100),
                    subtitle: "进度",
                    color: Color.hexColor("FFD700")
                )
            }
            
            // 最新徽章展示
            if let latestAchievement = viewModel.achievements.filter({ $0.isUnlocked }).first {
                HStack(spacing: 12) {
                    // 徽章图标
                    Text(latestAchievement.icon)
                        .font(.system(size: 24))
                        .frame(width: 40, height: 40)
                        .background(
                            Circle()
                                .fill(Color.white.opacity(0.1))
                        )
                    
                    // 徽章信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text("最新获得：\(latestAchievement.name)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                        
                        Text(latestAchievement.description)
                            .font(.system(size: 12, weight: .regular))
                            .foregroundColor(Color.white.opacity(0.7))
                    }
                    
                    Spacer()
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.05))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.hexColor("40E0D0").opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - Functions Section
    private var functionsSection: some View {
        VStack(spacing: 16) {
            // 标题
            HStack {
                Text("功能设置")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            // 功能列表
            VStack(spacing: 12) {
                // 成就殿堂
                functionItem(
                    icon: "trophy",
                    title: "成就殿堂",
                    subtitle: "查看所有徽章",
                    action: {
                        activeSheet = .achievementHall
                    }
                )
                
                // Pro会员中心
                functionItem(
                    icon: "crown",
                    title: "Pro会员中心",
                    subtitle: sessionManager.currentUser?.isPro == true ? "管理会员权益" : "升级Pro会员",
                    action: {
                        activeSheet = .proCenter
                    }
                )
                
                // 设置
                functionItem(
                    icon: "gearshape",
                    title: "设置",
                    subtitle: "通知、隐私等设置",
                    action: {
                        activeSheet = .settings
                    }
                )
                
                // 数据导出
                functionItem(
                    icon: "square.and.arrow.up",
                    title: "数据导出",
                    subtitle: isExporting ? "正在导出..." : "导出计划数据",
                    action: {
                        activeAlert = .dataExport
                    }
                )
                
                // 关于应用
                functionItem(
                    icon: "info.circle",
                    title: "关于应用",
                    subtitle: "版本信息和帮助",
                    action: {
                        activeSheet = .about
                    }
                )
                
                // 🔧 阶段4：数据持久化测试工具（仅DEBUG模式）
                #if DEBUG
                functionItem(
                    icon: "wrench.and.screwdriver",
                    title: "🔧 测试工具",
                    subtitle: "数据持久化验证（开发者模式）",
                    action: {
                        activeSheet = .dataPersistenceTest
                    }
                )
                #endif
            }
        }
    }
    
    // MARK: - Logout Section
    private var logoutSection: some View {
        VStack(spacing: 16) {
            // 分割线
            Rectangle()
                .fill(Color.white.opacity(0.1))
                .frame(height: 1)
            
            // 退出登录按钮
            Button(action: {
                activeAlert = .logout
            }) {
                HStack(spacing: 12) {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color.hexColor("FF6B6B"))
                    
                    Text("退出登录")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color.hexColor("FF6B6B"))
                    
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.hexColor("FF6B6B").opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.hexColor("FF6B6B").opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.top, 20)
    }
    
    // MARK: - Function Item
    private func functionItem(
        icon: String,
        title: String,
        subtitle: String,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标
                ZStack {
                    Circle()
                        .fill(Color.hexColor("40E0D0").opacity(0.2))
                        .frame(width: 44, height: 44)
                    
                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(Color.hexColor("40E0D0"))
                }
                
                // 文字信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.white.opacity(0.7))
                }
                
                Spacer()
                
                // 箭头
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.white.opacity(0.5))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Custom Edit Name Overlay
    @ViewBuilder
    private var editNameOverlay: some View {
        if showEditNameAlert {
            ZStack {
                // 背景遮罩 - 使用渐变模糊效果
                Rectangle()
                    .fill(.ultraThinMaterial)
                    .background(
                        LinearGradient(
                            colors: [
                                Color.black.opacity(0.6),
                                Color.hexColor("002b20").opacity(0.3)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .ignoresSafeArea(.all)
                    .onTapGesture {
                        withAnimation(.interactiveSpring(response: 0.5, dampingFraction: 0.8)) {
                            showEditNameAlert = false
                        }
                    }
                
                // 编辑弹窗 - 重新设计为更现代的卡片样式
                VStack(spacing: 0) {
                    // 顶部装饰条
                    RoundedRectangle(cornerRadius: 2)
                        .fill(Color.hexColor("40E0D0"))
                        .frame(width: 40, height: 4)
                        .padding(.top, 12)
                    
                    // 主要内容区域
                    VStack(spacing: 24) {
                        // 标题区域
                        VStack(spacing: 8) {
                            // 图标
                            Image(systemName: "person.circle.fill")
                                .font(.system(size: 32, weight: .medium))
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [Color.hexColor("40E0D0"), Color.hexColor("2dd4bf")],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                            
                            // 标题
                            Text("编辑用户名")
                                .font(.system(size: 20, weight: .semibold, design: .rounded))
                                .foregroundColor(.white)
                                .accessibilityAddTraits(.isHeader)
                            
                            // 副标题
                            Text("设置一个独特的用户名来个性化您的体验")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color.white.opacity(0.7))
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        
                        // 输入框区域 - 使用自定义样式
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "person")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(Color.hexColor("40E0D0"))
                                    .frame(width: 20)
                                
                                TextField("输入用户名", text: $editingName)
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                    .textInputAutocapitalization(.never)
                                    .autocorrectionDisabled()
                                    .textContentType(.username)
                                    .submitLabel(.done)
                                    .onSubmit {
                                        if !editingName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                                            handleSaveUserName()
                                            showEditNameAlert = false
                                        }
                                    }
                                
                                // 字符计数
                                Text("\(editingName.count)/20")
                                    .font(.system(size: 12, weight: .medium))
                                    .foregroundColor(
                                        editingName.count > 15 ? Color.hexColor("FF7F50") : Color.white.opacity(0.6)
                                    )
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 14)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(.ultraThinMaterial)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(
                                                LinearGradient(
                                                    colors: [
                                                        Color.hexColor("40E0D0").opacity(0.5),
                                                        Color.hexColor("2dd4bf").opacity(0.3)
                                                    ],
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                ),
                                                lineWidth: 1
                                            )
                                    )
                            )
                            
                            // 输入提示
                            if editingName.count > 15 {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .font(.system(size: 12))
                                        .foregroundColor(Color.hexColor("FF7F50"))
                                    
                                    Text("用户名即将达到最大长度")
                                        .font(.system(size: 12, weight: .medium))
                                        .foregroundColor(Color.hexColor("FF7F50"))
                                }
                                .transition(.opacity.combined(with: .move(edge: .top)))
                            }
                        }
                        .accessibilityElement(children: .contain)
                        .accessibilityLabel("用户名输入区域")
                        .accessibilityHint("输入您的新用户名，最多20个字符")
                        
                        // 按钮组 - 重新设计为更现代的样式
                        HStack(spacing: 12) {
                            // 取消按钮
                            Button(action: {
                                withAnimation(.interactiveSpring(response: 0.5, dampingFraction: 0.8)) {
                                    showEditNameAlert = false
                                }
                            }) {
                                Text("取消")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(Color.white.opacity(0.8))
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 48)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.white.opacity(0.1))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                            )
                                    )
                            }
                            .buttonStyle(ScaleButtonStyle())
                            
                            // 保存按钮
                            Button(action: {
                                withAnimation(.interactiveSpring(response: 0.5, dampingFraction: 0.8)) {
                                    handleSaveUserName()
                                    showEditNameAlert = false
                                }
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 14, weight: .semibold))
                                    
                                    Text("保存")
                                        .font(.system(size: 16, weight: .semibold))
                                }
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .frame(height: 48)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(
                                            LinearGradient(
                                                colors: editingName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? 
                                                [Color.gray.opacity(0.5), Color.gray.opacity(0.3)] :
                                                [Color.hexColor("40E0D0"), Color.hexColor("2dd4bf")],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(
                                                    editingName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?
                                                    Color.clear :
                                                    Color.white.opacity(0.2),
                                                    lineWidth: 1
                                                )
                                        )
                                )
                            }
                            .disabled(editingName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                            .buttonStyle(ScaleButtonStyle())
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 24)
                }
                .background(
                    // 玻璃拟态背景
                    RoundedRectangle(cornerRadius: 20)
                        .fill(.ultraThinMaterial)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(
                                    LinearGradient(
                                        colors: [
                                            Color.hexColor("002b20").opacity(0.8),
                                            Color.black.opacity(0.9)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    )
                                )
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(
                                    LinearGradient(
                                        colors: [
                                            Color.hexColor("40E0D0").opacity(0.3),
                                            Color.white.opacity(0.1)
                                        ],
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ),
                                    lineWidth: 1
                                )
                        )
                        .shadow(
                            color: Color.hexColor("40E0D0").opacity(0.2),
                            radius: 20,
                            x: 0,
                            y: 10
                        )
                )
                .padding(.horizontal, 32)
                .accessibilityElement(children: .contain)
                .accessibilityLabel("编辑用户名对话框")
            }
            .transition(.asymmetric(
                insertion: .opacity.combined(with: .scale(scale: 0.8)).combined(with: .move(edge: .bottom)),
                removal: .opacity.combined(with: .scale(scale: 0.95))
            ))
            .animation(.interactiveSpring(response: 0.6, dampingFraction: 0.8), value: showEditNameAlert)
        }
    }
    
    // MARK: - Helper Methods
    private func formatDate(_ dateString: String) -> String {
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "yyyy年MM月dd日"
            return displayFormatter.string(from: date)
        }
        return dateString
    }
    
    private func formatDateFromDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: date)
    }
    
    // MARK: - Actions
    private func handleLogout() {
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // 🔑 关键修复：强化版退出登录处理，增加用户反馈机制
        Task { @MainActor in
            do {
                // 🚀 新增：显示退出中的状态提示
                // 可以在这里添加Loading状态，但为了简洁暂时省略
                
                // 执行退出登录
            sessionManager.logout()
                
                // 🚀 新增：退出成功的触觉反馈
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)
                
                // 🚀 新增：短暂延迟确保UI更新完成
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                
                // 🔑 关键：强制验证退出状态
                if sessionManager.isLoggedIn {
                    // 如果退出失败，再次尝试
                    sessionManager.logout()
                    
                    // 发送警告反馈
                    let warningFeedback = UINotificationFeedbackGenerator()
                    warningFeedback.notificationOccurred(.warning)
                }
                
            } catch {
                // 🚀 新增：退出失败的错误处理
                await MainActor.run {
                    // 错误反馈
                    let errorFeedback = UINotificationFeedbackGenerator()
                    errorFeedback.notificationOccurred(.error)
                    
                    // 可以在这里显示错误提示
                    errorMessage = "退出登录失败，请重试"
                }
            }
        }
    }
    
    private func handleDataExport() {
        isExporting = true
        
        Task {
            do {
                guard let container = sessionManager.repositoryContainer else {
                    await MainActor.run {
                        isExporting = false
                        exportMessage = "数据访问未初始化"
                    }
                    return
                }
                
                let exportService = EADataExportService(sessionManager: sessionManager, repositoryContainer: container)
                let fileURL = try await exportService.exportAllData()
                
                await MainActor.run {
                    isExporting = false
                    shareFile(url: fileURL)
                }
            } catch {
                await MainActor.run {
                    isExporting = false
                    exportMessage = "导出失败：\(error.localizedDescription)"
                }
            }
        }
    }
    
    private func shareFile(url: URL) {
        let activityViewController = UIActivityViewController(
            activityItems: [url],
            applicationActivities: nil
        )
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            window.rootViewController?.present(activityViewController, animated: true)
        }
    }
    
    /// 保存用户名
    private func handleSaveUserName() {
        guard let currentUser = sessionManager.currentUser,
              let container = sessionManager.repositoryContainer else { return }
        
        // 验证用户名
        let trimmedName = editingName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty, trimmedName.count <= 20 else {
            // 可以添加错误提示
            return
        }
        
        // 更新用户名
        currentUser.username = trimmedName
        
        // 保存到数据库
        Task {
            do {
                try await container.userRepository.saveUser(currentUser)
                
                // 同步SessionManager信息
                await MainActor.run {
                    sessionManager.currentUser?.username = trimmedName
                    
                    // 触觉反馈提示保存成功
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                }
                
            } catch {
                await MainActor.run {
                    errorMessage = "保存失败：\(error.localizedDescription)"
                    
                    // 触觉反馈提示保存失败
                    let notificationFeedback = UINotificationFeedbackGenerator()
                    notificationFeedback.notificationOccurred(.error)
                }
            }
        }
    }
    
    // MARK: - Avatar Management
    
    /// 加载用户头像数据（已不需要，因为使用计算属性直接绑定）
    private func loadUserAvatarData() {
        // 🔑 不再需要手动加载，头像数据通过计算属性直接从SessionManager获取
        // 这样确保Tab切换时数据不会丢失
    }
    
    /// 通过SessionManager保存头像数据（新的架构优化方法）
    @MainActor
    private func saveAvatarDataThroughSessionManager(_ avatarData: EAAvatarData?) async {
        do {
            // 🔑 通过SessionManager的新方法保存并刷新用户数据
            try await sessionManager.updateUserAvatarAndRefresh(avatarData: avatarData)
            
            // 触觉反馈提示保存成功
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
        } catch {
            // 保存头像失败处理
            errorMessage = "保存头像失败：\(error.localizedDescription)"
            
            // 触觉反馈提示保存失败
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
        }
    }
    
    // MARK: - 计算内容区域最大高度
    private func calculateContentHeight(geometry: GeometryProxy) -> CGFloat {
        let screenHeight = geometry.size.height
        let topMaskHeight = calculateSafeTopMaskHeight(geometry: geometry) // 使用优化后的遮挡高度
        let bottomSafeArea = geometry.safeAreaInsets.bottom
        let tabBarHeight: CGFloat = 49 // iOS标准Tab栏高度
        
        // 可用内容高度 = 屏幕高度 - 顶部遮挡高度 - 底部安全区域 - Tab栏高度
        let availableHeight = screenHeight - topMaskHeight - bottomSafeArea - tabBarHeight
        
        return max(availableHeight, 300) // 确保最小高度
    }
    
    // MARK: - 计算安全的顶部遮挡高度（iOS规范）- 与"今日"页面完全一致
    private func calculateSafeTopMaskHeight(geometry: GeometryProxy) -> CGFloat {
        let topSafeArea = geometry.safeAreaInsets.top
        
        // 🔑 标准iOS设备安全区域高度计算
        // iOS设备安全区域高度参考：
        // - iPhone 15 Pro/Pro Max (Dynamic Island): 59pt
        // - iPhone 14 Pro/Pro Max (Dynamic Island): 59pt  
        // - iPhone 13/14/15 (Notch): 47pt
        // - iPhone 12 mini/13 mini: 50pt
        // - iPhone SE (无刘海): 20pt (状态栏高度)
        
        // ✅ 精确iOS规范：在系统安全区域基础上增加适量间距，确保与灵动岛/刘海有足够视觉分离
        let calculatedHeight: CGFloat
        if topSafeArea > 20 {
            // 有刘海/灵动岛设备：系统安全区域 + 额外视觉间距
            let extraSpacing: CGFloat
            if topSafeArea >= 55 {
                // 灵动岛设备 (iPhone 14 Pro+)：增加10pt额外间距
                extraSpacing = 10
            } else {
                // 刘海设备 (iPhone X-13)：增加8pt额外间距
                extraSpacing = 8
            }
            calculatedHeight = topSafeArea + extraSpacing
        } else {
            // 无刘海设备：使用状态栏高度，无需额外间距
            calculatedHeight = 20
        }
        
        // 🎯 内容区域额外间距：在安全区域基础上再增加内容缓冲区
        let additionalContentSpacing: CGFloat = 12 // 额外的内容区域间距
        
        return calculatedHeight + additionalContentSpacing
    }
    
    // MARK: - Phase 3 Day 7: 档案模式选择器
    
    private var profileModeSelector: some View {
        HStack(spacing: 12) {
            ForEach(ProfileDisplayMode.allCases, id: \.self) { mode in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        profileMode = mode
                        if mode == .cosmic {
                            activeSheet = .cosmicProfile
                        }
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: mode.icon)
                            .font(.system(size: 14, weight: .medium))
                        
                        Text(mode.title)
                            .font(.subheadline.weight(.medium))
                    }
                    .foregroundStyle(profileMode == mode ? .white : .secondary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        ZStack {
                            if profileMode == mode {
                                // 选中状态背景
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(
                                        LinearGradient(
                                            colors: mode == .cosmic 
                                                ? [.blue, .purple] 
                                                : [.accentColor, .accentColor.opacity(0.8)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(.white.opacity(0.3), lineWidth: 1)
                                    )
                            } else {
                                // 未选中状态背景
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(.ultraThinMaterial)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20)
                                            .stroke(.secondary.opacity(0.3), lineWidth: 1)
                                    )
                            }
                        }
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Phase 3 Day 7: 档案显示模式枚举

enum ProfileDisplayMode: String, CaseIterable {
    case classic = "classic"
    case cosmic = "cosmic"
    
    var title: String {
        switch self {
        case .classic:
            return "经典档案"
        case .cosmic:
            return "星际档案"
        }
    }
    
    var icon: String {
        switch self {
        case .classic:
            return "person.circle.fill"
        case .cosmic:
            return "sparkles"
        }
    }
}



// MARK: - Custom Button Styles

/// 缩放按钮样式 - 提供触觉反馈
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.96 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
            .onChange(of: configuration.isPressed) { _, isPressed in
                if isPressed {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }
            }
    }
}

// MARK: - Achievement Stat Card
private struct AchievementStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 6) {
            Text(title)
                .font(.system(size: 11, weight: .medium))
                .foregroundColor(Color.white.opacity(0.7))
            
            Text(value)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(color)
            
            Text(subtitle)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(Color.white.opacity(0.6))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview
#Preview("我的页面") {
    @MainActor
    func createPreview() -> some View {
        let container = PreviewData.container
        let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
        let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
        let viewModel = EAMeViewModel(sessionManager: sessionManager)
        
        return NavigationView {
            EAMeView(viewModel: viewModel)
                .environmentObject(sessionManager)
        }
        .modelContainer(container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}

#Preview("我的页面 - 普通用户") {
    @MainActor
    func createPreview() -> some View {
        let container = PreviewData.container
        let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
        let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
        let viewModel = EAMeViewModel(sessionManager: sessionManager)
        
        return NavigationView {
            EAMeView(viewModel: viewModel)
                .environmentObject(sessionManager)
        }
        .modelContainer(container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
}

#Preview("我的页面 - Pro用户") {
    @MainActor
    func createPreview() -> some View {
        let container = PreviewData.container
        let repositoryContainer = EARepositoryContainerImpl(modelContainer: container)
        let sessionManager = EASessionManager(repositoryContainer: repositoryContainer)
        let viewModel = EAMeViewModel(sessionManager: sessionManager)
        
        return NavigationView {
            EAMeView(viewModel: viewModel)
                .environmentObject(sessionManager)
        }
        .modelContainer(container)
        .preferredColorScheme(.dark)
    }
    
    return createPreview()
} 