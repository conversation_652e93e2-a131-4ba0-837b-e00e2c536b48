import Foundation
import SwiftData
import SwiftUI
import UserNotifications

// MARK: - Today页面ViewModel
/// Today页面的数据管理和业务逻辑处理
/// 
/// 🔑 核心功能：
/// - 管理今日习惯完成状态
/// - 处理AI消息提示
/// - 生成每日洞察内容
/// - 响应数据变化通知
@MainActor
class EATodayViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    @Published var currentDate: Date = Date()
    @Published var dailyInsight: String = ""
    @Published var completedHabitIds: Set<UUID> = []
    @Published var todayProgress: Double = 0.0
    
    // ✅ 新增：今日习惯数据
    @Published var todayHabits: [EAHabit] = []
    
    // AI消息相关属性
    @Published var hasAIMessages: Bool = false
    @Published var aiMessageCount: Int = 0
    
    // MARK: - Private Properties
    private var repositoryContainer: EARepositoryContainer?
    private var notificationObservers: [NSObjectProtocol] = []
    private let sessionManager: EASessionManager
    
    // MARK: - Computed Properties
    
    /// 当前登录用户
    var currentUser: EAUser? {
        return sessionManager.currentUser
    }
    
    /// 今日日期字符串（中文格式）
    var todayDateString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "M月d日"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentDate)
    }
    
    /// 星期字符串（中文格式）
    var weekdayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: currentDate)
    }
    
    // MARK: - Initialization
    
    /// 初始化Today页面ViewModel
    /// - Parameter sessionManager: 会话管理器，管理用户登录状态
    /// ✅ 修复：强制Repository模式，移除ModelContext依赖
    init(sessionManager: EASessionManager) {
        self.sessionManager = sessionManager
        self.currentDate = Date()
        self.dailyInsight = generateDailyInsight()
        
        // 设置通知监听
        setupNotificationObservers()
    }
    
    deinit {
        // 清理通知观察者
        notificationObservers.forEach { NotificationCenter.default.removeObserver($0) }
    }
    
    // MARK: - Public Methods
    
    /// 设置Repository容器
    /// - Parameter container: Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
        Task {
            await refreshCompletionStatus()
        }
    }
    

    
    /// ✅ 修复：过滤用户习惯（包含今日过滤逻辑）
    /// - Parameter allHabits: 所有习惯数据
    /// - Returns: 当前用户今日需要打卡的习惯
    func filterUserHabits(from allHabits: [EAHabit]) -> [EAHabit] {
        guard let currentUser = sessionManager.currentUser else {
            return []
        }
        
        // 🔑 关键修复：在ViewModel中处理关系链查询，避免SwiftData @Query的TERNARY错误
        let userHabits = allHabits.filter { habit in
            guard let habitUserId = habit.user?.id else { return false }
            return habitUserId == currentUser.id
        }
        
        // 🚀 关键修复：筛选当天需要打卡的习惯
        let todayRequiredHabits = filterTodayRequiredHabits(from: userHabits)
        
        return todayRequiredHabits
    }
    
    /// ✅ 修复：过滤当天需要打卡的习惯（新增公共方法）
    /// - Parameter habits: 用户的所有习惯
    /// - Returns: 当天需要打卡的习惯列表
    func filterTodayRequiredHabits(from habits: [EAHabit]) -> [EAHabit] {
        let calendar = Calendar.current
        let today = currentDate
        let weekday = calendar.component(.weekday, from: today) // 1=周日, 2=周一, ..., 7=周六
        let dayOfMonth = calendar.component(.day, from: today)
        
        return habits.filter { habit in
            guard habit.isActive else { 
                return false 
            }
            
            let shouldShow: Bool
            switch habit.frequencyType {
            case "daily":
                // 每日习惯：每天都需要打卡
                shouldShow = true
                
            case "weekly":
                // 每周习惯：检查今天是否在选定的星期几中
                let selectedWeekdays = habit.selectedWeekdays
                
                // 🔑 关键修复：正确转换iOS weekday到我们的格式
                // iOS: 1=周日, 2=周一, 3=周二, 4=周三, 5=周四, 6=周五, 7=周六
                // 我们的格式: 1=周一, 2=周二, 3=周三, 4=周四, 5=周五, 6=周六, 7=周日
                let ourWeekday: Int
                if weekday == 1 {
                    ourWeekday = 7 // 周日
                } else {
                    ourWeekday = weekday - 1 // 周一到周六
                }
                
                shouldShow = selectedWeekdays.contains(ourWeekday)
                
            case "monthly":
                // 🚨 关键修复：月度习惯需要区分两种模式
                let monthlyMode = habit.monthlyMode
                
                if monthlyMode == "target" {
                    // 按次数目标模式：每天都显示，让用户随时打卡直到达到月度目标
                    shouldShow = true
                } else if monthlyMode == "dates" {
                    // 按指定日期模式：只在选定日期显示
                    let selectedDates = habit.selectedMonthlyDates
                    shouldShow = selectedDates.contains(dayOfMonth)
                } else {
                    // 未知模式，默认显示（保守处理）
                    shouldShow = true
                }
                
            default:
                // 未知频率类型：默认显示
                shouldShow = true
            }
            
            return shouldShow
        }
    }
    
    /// ✅ 新增：过滤今日完成记录
    /// - Parameters:
    ///   - allCompletions: 所有完成记录
    ///   - date: 目标日期
    /// - Returns: 指定日期的完成记录
    func filterTodayCompletions(from allCompletions: [EACompletion], for date: Date) -> [EACompletion] {
        let calendar = Calendar.current
        return allCompletions.filter { completion in
            calendar.isDate(completion.date, inSameDayAs: date)
        }
    }
    
    /// ✅ 新增：计算已完成习惯数量
    /// - Parameters:
    ///   - habits: 习惯列表
    ///   - completions: 完成记录列表
    /// - Returns: 已完成的习惯数量
    func calculateCompletedCount(habits: [EAHabit], completions: [EACompletion]) -> Int {
        return habits.filter { habit in
            completions.contains(where: { $0.habit?.id == habit.id })
        }.count
    }
    
    /// ✅ 新增：加载今日数据（包括今日习惯）
    func loadTodayData() {
        currentDate = Date()
        dailyInsight = generateDailyInsight()
        Task {
            await loadTodayHabits()
            await refreshCompletionStatus()
        }
    }
    
    /// ✅ 新增：加载今日习惯数据
    private func loadTodayHabits() async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = sessionManager.currentUser else {
            await MainActor.run {
                self.todayHabits = []
            }
            return
        }
        
        do {
            // 1. 获取用户的所有活跃习惯
            let allUserHabits = try await repositoryContainer.habitRepository.fetchActiveHabits(for: currentUser.id)
            
            // 2. 过滤出今日需要打卡的习惯
            let filteredHabits = filterTodayRequiredHabits(from: allUserHabits)
            
            await MainActor.run {
                self.todayHabits = filteredHabits
            }
        } catch {
            await MainActor.run {
                self.todayHabits = []
                self.errorMessage = "加载今日习惯失败: \(error.localizedDescription)"
            }
        }
    }
    
    /// 刷新完成状态 - 通过Repository
    /// 
    /// 检查今日习惯完成情况并更新UI状态
    func refreshCompletionStatus() async {
        guard let repositoryContainer = repositoryContainer else {
            await MainActor.run {
                self.completedHabitIds = []
                self.isLoading = false
            }
            return
        }
        
        isLoading = true
        errorMessage = nil
            
        await calculateTodayProgress()
        await checkAIMessageStatus()
        
        isLoading = false
    }
    
    /// ✅ 修复：切换习惯完成状态 - 集成星际能量奖励系统
    /// - Parameter habit: 要切换完成状态的习惯
    /// 
    /// 🔑 功能说明：
    /// - 检查今日是否已完成该习惯
    /// - 若已完成则移除记录并回退能量，若未完成则添加记录并奖励能量
    /// - 通过Repository确保数据一致性
    /// - 集成星际能量奖励系统
    func toggleHabitCompletion(_ habit: EAHabit) {
        guard let repositoryContainer = repositoryContainer else { return }
        guard let currentUser = sessionManager.currentUser else { return }
        
        Task {
            do {
                let isCompleted = completedHabitIds.contains(habit.id)
                
                if isCompleted {
                    // ✅ 取消打卡：删除完成记录并回退能量
                    let todayCompletions = try await repositoryContainer.completionRepository.fetchTodayCompletions(for: currentUser.id)
                    if let completion = todayCompletions.first(where: { $0.habit?.id == habit.id }) {
                        // 计算需要回退的能量
                        let energyToDeduct = await calculateEnergyToDeduct(for: habit, completion: completion)
                        
                        // 删除完成记录
                        try await repositoryContainer.completionRepository.deleteCompletion(completion)
                        
                        // 回退星际能量
                        await deductStellarEnergy(userId: currentUser.id, amount: energyToDeduct, reason: "取消打卡")
                        
                        await MainActor.run {
                            _ = self.completedHabitIds.remove(habit.id)
                        }
                    }
                } else {
                    // ✅ 完成打卡：创建完成记录并奖励能量
                    _ = try await repositoryContainer.completionRepository.createCompletion(for: habit.id, note: nil, energyLevel: 5)
                    
                    // 计算连续完成状态
                    let isConsecutive = await checkIsConsecutiveCompletion(habitId: habit.id, userId: currentUser.id)
                    
                    // 奖励星际能量
                    let energyGained = await awardStellarEnergy(
                        habitId: habit.id,
                        userId: currentUser.id,
                        isConsecutive: isConsecutive
                    )
                    
                    await MainActor.run {
                        _ = self.completedHabitIds.insert(habit.id)
                    }
                    
                    // 发送能量奖励通知
                    await showEnergyRewardNotification(energyGained: energyGained, habitName: habit.name)
                }
                
                sendHabitDataChangedNotification(habitId: habit.id)
                
            } catch {
                await MainActor.run {
                    self.errorMessage = "操作失败，请重试"
                }
            }
        }
    }
    
    /// 刷新数据（公共方法）
    /// 
    /// 重新加载当前日期和每日洞察，并刷新完成状态
    func refreshData() async {
        currentDate = Date()
        dailyInsight = generateDailyInsight()
        await refreshCompletionStatus()
    }
    
    /// 获取习惯连续天数
    /// - Parameter habit: 目标习惯
    /// - Returns: 连续完成天数
    func getHabitStreak(_ habit: EAHabit) -> Int {
        return habit.currentStreak
    }
    
    /// 检查习惯是否已完成（使用缓存状态）
    /// - Parameter habit: 要检查的习惯
    /// - Returns: 是否已完成
    func isHabitCompleted(_ habit: EAHabit) -> Bool {
        return completedHabitIds.contains(habit.id)
    }
    
    /// 导航到Aura灵境空间
    /// 
    /// 发送导航通知，由主应用处理页面切换
    func navigateToAuraSpace() {
        NotificationCenter.default.post(
            name: NSNotification.Name(EAAppConstants.Today.Notifications.navigateToAuraSpace),
            object: nil
        )
    }
    
    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }
    
    /// 检查AI消息状态
    /// 
    /// 根据系统推送权限状态决定是否显示AI消息提示
    func checkAIMessageStatus() async {
        let notificationCenter = UNUserNotificationCenter.current()
        let settings = await notificationCenter.notificationSettings()
        
        if settings.authorizationStatus != .authorized {
            updateAIMessageStatus()
        } else {
            hasAIMessages = false
            aiMessageCount = 0
        }
    }
    
    // MARK: - Private Methods - 习惯完成状态管理
    
    /// 发送习惯数据变化通知
    /// - Parameter habitId: 发生变化的习惯ID
    private func sendHabitDataChangedNotification(habitId: UUID) {
        NotificationCenter.default.post(
            name: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
            object: habitId
        )
    }
    
    /// 获取今日日期范围
    /// - Returns: (今日开始时间, 明日开始时间)
    private func getTodayDateRange() -> (Date, Date) {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today) ?? today
        return (today, tomorrow)
    }
    
    // MARK: - Private Methods - 数据管理
    
    /// 设置通知观察者
    private func setupNotificationObservers() {
        // 监听习惯数据变更通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name(EAAppConstants.Today.Notifications.habitDataChanged),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
        
        // ✅ 关键新增：监听习惯创建通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EAHabitCreated"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                // 习惯创建后立即重新加载今日习惯列表
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
        
        // 监听会话变更通知
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("EASessionUserChanged"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task {
                await self?.loadTodayHabits()
                await self?.refreshCompletionStatus()
            }
        }
    }
    
    /// 处理习惯删除
    /// - Parameter habitId: 被删除的习惯ID
    private func handleHabitDeleted(habitId: UUID) {
        completedHabitIds.remove(habitId)
    }
    
    /// 计算今日进度 - 通过Repository
    private func calculateTodayProgress() async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = sessionManager.currentUser else { 
            await MainActor.run {
            self.completedHabitIds = []
                self.todayProgress = 0.0
            }
            return 
        }
        
        do {
            // 通过Repository获取今日完成记录
            let todayCompletions = try await repositoryContainer.completionRepository.fetchTodayCompletions(for: currentUser.id)
            
            let completedHabitIds = Set(todayCompletions.compactMap { completion in
                completion.habit?.id
            })
            
            // 获取用户活跃习惯总数来计算进度
            let activeHabits = try await repositoryContainer.habitRepository.fetchActiveHabits(for: currentUser.id)
            
            await MainActor.run {
            self.completedHabitIds = completedHabitIds
                self.todayProgress = activeHabits.isEmpty ? 0.0 : Double(completedHabitIds.count) / Double(activeHabits.count)
            }
            
        } catch {
            await MainActor.run {
                self.completedHabitIds = []
                self.todayProgress = 0.0
            }
        }
    }
    
    /// 更新AI消息状态
    private func updateAIMessageStatus() {
        let shouldShowAIMessage = completedHabitIds.count < 3
        hasAIMessages = shouldShowAIMessage
        aiMessageCount = shouldShowAIMessage ? Int.random(in: 1...3) : 0
    }
    
    /// 生成每日洞察
    /// - Returns: 随机选择的每日洞察文案
    private func generateDailyInsight() -> String {
        let insights = EAAppConstants.Today.Text.dailyInsights
        return insights.randomElement() ?? insights[0]
    }
    
    /// 加载用户历史数据（暂时禁用）
    private func loadCompletionHistory() {
        // 功能暂时禁用，等待Repository实现
    }
    
    // MARK: - ✅ 新增：星际能量奖励系统集成
    
    /// 奖励星际能量
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    ///   - isConsecutive: 是否连续完成
    /// - Returns: 获得的能量值
    private func awardStellarEnergy(habitId: UUID, userId: UUID, isConsecutive: Bool) async -> Int {
        // 创建星际能量服务实例
        guard let repositoryContainer = repositoryContainer else { return 0 }
        
        let cacheManager = EAAICacheManager()
        let energyService = EAStellarEnergyService(
            repositoryContainer: repositoryContainer,
            cacheManager: cacheManager
        )
        
        // 计算并奖励星际能量
        let energyGained = await energyService.calculateHabitCompletionEnergy(
            habitId: habitId,
            userId: userId,
            isConsecutive: isConsecutive
        )
        
        return energyGained
    }
    
    /// 回退星际能量
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - amount: 回退数量
    ///   - reason: 回退原因
    private func deductStellarEnergy(userId: UUID, amount: Int, reason: String) async {
        guard let repositoryContainer = repositoryContainer else { return }
        
        // 获取用户社交档案
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        guard let socialProfile = user?.socialProfile else { return }
        
        // 回退能量（确保不会变成负数）
        let currentEnergy = socialProfile.totalStellarEnergy ?? 0
        let newEnergy = max(0, currentEnergy - amount)
        socialProfile.totalStellarEnergy = newEnergy
        
        // 记录能量变化历史
        socialProfile.recordDailyEnergyChange(-amount, reason: reason)
    }
    
    /// 计算需要回退的能量
    /// - Parameters:
    ///   - habit: 习惯
    ///   - completion: 完成记录
    /// - Returns: 需要回退的能量值
    private func calculateEnergyToDeduct(for habit: EAHabit, completion: EACompletion) async -> Int {
        // 基础能量回退（与奖励逻辑对应）
        var baseEnergy = 10 // 基础习惯完成能量
        
        // 根据习惯难度调整
        switch habit.targetFrequency {
        case 1...3:
            baseEnergy = Int(Double(baseEnergy) * 1.0) // 简单习惯
        case 4...6:
            baseEnergy = Int(Double(baseEnergy) * 1.2) // 中等习惯
        case 7...:
            baseEnergy = Int(Double(baseEnergy) * 1.5) // 困难习惯
        default:
            baseEnergy = 10
        }
        
        // 如果是连续完成，需要回退连击奖励
        let streak = await calculateCurrentStreak(habitId: habit.id)
        if streak > 0 {
            let streakBonus = calculateStreakBonus(streak: streak)
            baseEnergy += streakBonus
        }
        
        return baseEnergy
    }
    
    /// 检查是否为连续完成
    /// - Parameters:
    ///   - habitId: 习惯ID
    ///   - userId: 用户ID
    /// - Returns: 是否连续完成
    private func checkIsConsecutiveCompletion(habitId: UUID, userId: UUID) async -> Bool {
        guard let repositoryContainer = repositoryContainer else { return false }
        
        do {
            // 获取最近的完成记录
            let recentCompletions = try await repositoryContainer.completionRepository.fetchRecentCompletions(userId: userId, days: 7)
            let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
            
            // 检查昨天是否有完成记录
            let calendar = Calendar.current
            let yesterday = calendar.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            let yesterdayStart = calendar.startOfDay(for: yesterday)
            let yesterdayEnd = calendar.date(byAdding: .day, value: 1, to: yesterdayStart) ?? yesterdayStart
            
            let hasYesterdayCompletion = habitCompletions.contains { completion in
                completion.date >= yesterdayStart && completion.date < yesterdayEnd
            }
            
            return hasYesterdayCompletion
            
        } catch {
            return false
        }
    }
    
    /// 计算当前连击天数
    /// - Parameter habitId: 习惯ID
    /// - Returns: 连击天数
    private func calculateCurrentStreak(habitId: UUID) async -> Int {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = sessionManager.currentUser else { return 0 }
        
        do {
            let recentCompletions = try await repositoryContainer.completionRepository.fetchRecentCompletions(userId: currentUser.id, days: 365)
            let habitCompletions = recentCompletions.filter { $0.habit?.id == habitId }
            
            let calendar = Calendar.current
            let today = Date()
            var streak = 0
            
            // 从今天开始往前计算连击
            for day in 0..<365 {
                let checkDate = calendar.date(byAdding: .day, value: -day, to: today)!
                let dayStart = calendar.startOfDay(for: checkDate)
                let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
                
                let hasCompletion = habitCompletions.contains { completion in
                    completion.date >= dayStart && completion.date < dayEnd
                }
                
                if hasCompletion {
                    streak += 1
                } else {
                    break
                }
            }
            
            return streak
            
        } catch {
            return 0
        }
    }
    
    /// 计算连击奖励
    /// - Parameter streak: 连击天数
    /// - Returns: 奖励能量
    private func calculateStreakBonus(streak: Int) -> Int {
        switch streak {
        case 3...6:
            return 5
        case 7...13:
            return 10
        case 14...29:
            return 20
        case 30...:
            return 50
        default:
            return 0
        }
    }
    
    /// 显示能量奖励通知
    /// - Parameters:
    ///   - energyGained: 获得的能量
    ///   - habitName: 习惯名称
    private func showEnergyRewardNotification(energyGained: Int, habitName: String) async {
        await MainActor.run {
            // 这里可以显示一个临时的能量奖励提示
            // 可以通过NotificationCenter发送通知给UI层显示
            NotificationCenter.default.post(
                name: NSNotification.Name("EAStellarEnergyGained"),
                object: nil,
                userInfo: [
                    "energyGained": energyGained,
                    "habitName": habitName,
                    "message": "完成「\(habitName)」获得 \(energyGained) 星际能量！"
                ]
            )
        }
    }
}

// MARK: - Preview Support
extension EATodayViewModel {
    /// 预览用的ViewModel实例
    static var preview: EATodayViewModel {
        // ✅ 修复：使用临时SessionManager创建预览实例
        let viewModel = EATodayViewModel(sessionManager: EASessionManager())
        
        // 模拟数据
        viewModel.completedHabitIds = [UUID()]
        viewModel.dailyInsight = "每一个微小的进步，都是生态系统的能量流转"
        
        return viewModel
    }
} 